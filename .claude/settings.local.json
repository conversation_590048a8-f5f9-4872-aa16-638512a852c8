{"permissions": {"allow": ["<PERSON><PERSON>(python:*)", "Bash(grep:*)", "Bash(ls:*)", "Bash(find:*)", "Bash(/mnt/anaconda3/envs/base2/bin/python:*)", "<PERSON><PERSON>(timeout:*)", "Bash(JINA_API_KEY=jina_1647cb729b454f82ada95dedf18f5fbd_cxKj3hRLTs9MEwqUTxD64W3PbcF timeout 10 /mnt/anaconda3/envs/base2/bin/python -m moatless_mcp.server --workspace . --debug --no-index)", "Bash(PYTHONPATH=/mnt/projects/moatless-tools/moatless-mcp-server/src JINA_API_KEY=jina_1647cb729b454f82ada95dedf18f5fbd_cxKj3hRLTs9MEwqUTxD64W3PbcF timeout 10 /mnt/anaconda3/envs/base2/bin/python -m moatless_mcp.server --workspace . --debug --no-index)", "Bash(rm:*)", "Bash(JINA_API_KEY=\"jina_1647cb729b454f82ada95dedf18f5fbd_cxKj3hRLTs9MEwqUTxD64W3PbcF\" PYTHONPATH=\"/mnt/projects/moatless-tools/moatless-mcp-server/src\" /mnt/anaconda3/envs/base2/bin/python -m moatless_mcp.server --workspace /mnt/projects/moatless-tools --rebuild-index)", "Bash(JINA_API_KEY=\"jina_1647cb729b454f82ada95dedf18f5fbd_cxKj3hRLTs9MEwqUTxD64W3PbcF\" PYTHONPATH=\"/mnt/projects/moatless-tools/moatless-mcp-server/src\" timeout 60s /mnt/anaconda3/envs/base2/bin/python -m moatless_mcp.server --workspace . --rebuild-index)", "Bash(JINA_API_KEY=\"jina_1647cb729b454f82ada95dedf18f5fbd_cxKj3hRLTs9MEwqUTxD64W3PbcF\" PYTHONPATH=\"/mnt/projects/moatless-tools/moatless-mcp-server/src\" /mnt/anaconda3/envs/base2/bin/python -c \"\ntry:\n    from moatless.index import CodeIndex\n    from moatless.index.settings import IndexSettings\n    from moatless.repository import FileRepository\n    print('✅ 基础导入成功')\n    \n    # 测试文件仓库\n    file_repo = FileRepository(repo_path='/mnt/projects/moatless-tools')\n    print('✅ FileRepository 创建成功')\n    \n    # 测试设置\n    settings = IndexSettings(\n        language='python',\n        embed_model='jina-embeddings-v3',\n        dimensions=1024,\n        min_chunk_size=500,\n        chunk_size=2000,\n        hard_token_limit=4000,\n        max_chunks=50\n    )\n    print('✅ IndexSettings 创建成功')\n    \n    # 测试CodeIndex创建\n    code_index = CodeIndex(\n        file_repo=file_repo,\n        settings=settings,\n        max_results=25\n    )\n    print('✅ CodeIndex 创建成功')\n    \nexcept Exception as e:\n    print(f'❌ 错误: {e}')\n    import traceback\n    traceback.print_exc()\n\")", "<PERSON><PERSON>(pkill:*)", "Bash(kill:*)", "Bash(PYTHONPATH=\"/mnt/projects/moatless-tools/moatless-mcp-server/src\" /mnt/anaconda3/envs/base2/bin/python -m moatless_mcp.server --help)", "<PERSON><PERSON>(mkdir:*)", "WebFetch(domain:jina.ai)"], "deny": []}}