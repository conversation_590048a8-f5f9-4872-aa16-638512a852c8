{"base_config": {"temperature": 0.0, "thoughts_in_action": false, "max_tokens": 4000, "few_shot_examples": true, "timeout": 120.0, "message_cache": true, "disable_thoughts": false, "merge_same_role_messages": false}, "models": {"claude-3-5-sonnet-20241022": {"completion_model_class": "moatless.completion.tool_call.ToolCallCompletionModel", "model": "claude-3-5-sonnet-20241022"}, "claude-3-7-sonnet-20250219": {"completion_model_class": "moatless.completion.tool_call.ToolCallCompletionModel", "model": "claude-3-7-sonnet-20250219", "headers": {"anthropic-beta": "token-efficient-tools-2025-02-19,computer-use-2025-01-24,prompt-caching-2024-07-31"}}, "claude-3-5-haiku-20241022": {"completion_model_class": "moatless.completion.tool_call.ToolCallCompletionModel", "model": "claude-3-5-haiku-20241022"}, "gpt-4o-2024-11-20": {"completion_model_class": "moatless.completion.tool_call.ToolCallCompletionModel", "model": "gpt-4o-2024-11-20", "thoughts_in_action": true}, "gpt-4o-mini-2024-07-18": {"completion_model_class": "moatless.completion.tool_call.ToolCallCompletionModel", "model": "gpt-4o-mini-2024-07-18", "thoughts_in_action": true, "max_tokens": 8000}, "o1-preview-2024-09-12": {"completion_model_class": "moatless.completion.react.ReActCompletionModel", "model": "o1-preview-2024-09-12", "max_tokens": 8000}, "o1-mini-2024-09-12": {"completion_model_class": "moatless.completion.react.ReActCompletionModel", "model": "o1-mini-2024-09-12", "disable_thoughts": true, "max_tokens": 8000}, "o3-mini-2025-01-31": {"completion_model_class": "moatless.completion.tool_call.ToolCallCompletionModel", "model": "openai/o3-mini-2025-01-31", "thoughts_in_action": true, "max_tokens": null, "temperature": null}, "deepseek-chat": {"completion_model_class": "moatless.completion.react.ReActCompletionModel", "model": "deepseek/deepseek-chat"}, "deepseek-reasoner": {"completion_model_class": "moatless.completion.react.ReActCompletionModel", "model": "deepseek/deepseek-reasoner", "temperature": null, "disable_thoughts": true, "few_shot_examples": true, "merge_same_role_messages": true, "max_tokens": 8000}, "gemini-exp-1206": {"completion_model_class": "moatless.completion.react.ReActCompletionModel", "model": "gemini/gemini-exp-1206"}, "gemini-2.0-flash": {"completion_model_class": "moatless.completion.react.ReActCompletionModel", "model": "gemini/gemini-2.0-flash", "max_tokens": 8000}, "gemini-2.0-flash-exp": {"completion_model_class": "moatless.completion.react.ReActCompletionModel", "model": "gemini/gemini-2.0-flash-exp", "max_tokens": 8000}, "gemini-2.0-flash-thinking-exp": {"completion_model_class": "moatless.completion.react.ReActCompletionModel", "model": "gemini/gemini-2.0-flash-thinking-exp", "max_tokens": 8000}, "llama-3.1-405b-instruct": {"completion_model_class": "moatless.completion.react.ReActCompletionModel", "model": "openrouter/meta-llama/llama-3.1-405b-instruct"}, "llama-3.1-70b-instruct": {"completion_model_class": "moatless.completion.react.ReActCompletionModel", "model": "openrouter/meta-llama/llama-3.1-70b-instruct"}, "qwen-2.5-coder-32b-instruct": {"completion_model_class": "moatless.completion.react.ReActCompletionModel", "model": "qwen/qwen-2.5-coder-32b-instruct"}}, "supported_models": ["claude-3-5-sonnet-20241022", "claude-3-5-haiku-20241022", "o1-mini-2024-09-12", "gpt-4o-2024-11-20", "gpt-4o-mini-2024-07-18", "gemini-exp-1206", "gemini-2.0-flash-exp", "deepseek-chat", "deepseek-reasoner", "llama-3.1-70b-instruct", "qwen-2.5-coder-32b-instruct"]}