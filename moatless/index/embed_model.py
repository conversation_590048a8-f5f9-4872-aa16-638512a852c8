import os
import requests
from typing import Optional, List

from llama_index.core.embeddings import BaseEmbedding


class JinaAIEmbedding(BaseEmbedding):
    """Custom Jina AI embedding model implementation."""
    
    def __init__(self, model_name: str = "jina-embeddings-v3", api_key: Optional[str] = None, **kwargs):
        super().__init__(model_name=model_name, **kwargs)
        
        self._api_key = api_key or os.environ.get("JINA_API_KEY")
        
        if not self._api_key:
            raise ValueError("JINA_API_KEY environment variable is not set. Please set it to your Jina API key.")
        
        self._url = "https://api.jina.ai/v1/embeddings"
        self._headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self._api_key}"
        }
    
    def _get_query_embedding(self, query: str) -> List[float]:
        """Get embedding for a single query."""
        return self._get_embeddings([query], task="retrieval.query")[0]
    
    def _get_text_embedding(self, text: str) -> List[float]:
        """Get embedding for a single text."""
        return self._get_embeddings([text], task="retrieval.passage")[0]
    
    def _get_text_embeddings(self, texts: List[str]) -> List[List[float]]:
        """Get embeddings for multiple texts."""
        return self._get_embeddings(texts, task="retrieval.passage")
    
    def get_text_embeddings(self, texts: List[str]) -> List[List[float]]:
        """Public method to get embeddings for multiple texts."""
        return self._get_text_embeddings(texts)
    
    async def _aget_query_embedding(self, query: str) -> List[float]:
        """Async version of _get_query_embedding."""
        return self._get_query_embedding(query)
    
    async def _aget_text_embedding(self, text: str) -> List[float]:
        """Async version of _get_text_embedding."""
        return self._get_text_embedding(text)
    
    async def _aget_text_embeddings(self, texts: List[str]) -> List[List[float]]:
        """Async version of _get_text_embeddings."""
        return self._get_text_embeddings(texts)
    
    def _get_embeddings(self, texts: List[str], task: str = "retrieval.passage") -> List[List[float]]:
        """Get embeddings from Jina AI API."""
        data = {
            "model": self.model_name,
            "task": task,
            "input": texts
        }
        
        try:
            response = requests.post(self._url, headers=self._headers, json=data)
            response.raise_for_status()
            
            result = response.json()
            return [item["embedding"] for item in result["data"]]
            
        except requests.exceptions.RequestException as e:
            raise RuntimeError(f"Failed to get embeddings from Jina AI: {e}")
        except KeyError as e:
            raise RuntimeError(f"Unexpected response format from Jina AI: {e}")


def get_embed_model(model_name: str) -> Optional[BaseEmbedding]:
    if model_name.startswith("voyage"):
        try:
            from llama_index.embeddings.voyageai import VoyageEmbedding
        except ImportError as e:
            raise ImportError(
                "llama-index-embeddings-voyageai is not installed. Please install it using `pip install llama-index-embeddings-voyageai`"
            ) from e

        if "VOYAGE_API_KEY" not in os.environ:
            raise ValueError("VOYAGE_API_KEY environment variable is not set. Please set it to your Voyage API key.")

        return VoyageEmbedding(
            model_name=model_name,
            voyage_api_key=os.environ.get("VOYAGE_API_KEY"),
            truncation=True,
            embed_batch_size=60,
        )
    elif model_name.startswith("jina"):
        # Use Jina AI embeddings
        return JinaAIEmbedding(model_name=model_name)
    else:
        # Assumes OpenAI otherwise
        try:
            from llama_index.embeddings.openai import OpenAIEmbedding
        except ImportError as e:
            raise ImportError(
                "llama-index-embeddings-openai is not installed. Please install it using `pip install llama-index-embeddings-openai`"
            ) from e

        return OpenAIEmbedding(model_name=model_name)
