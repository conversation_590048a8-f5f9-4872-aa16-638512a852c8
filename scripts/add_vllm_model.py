#!/usr/bin/env python3
"""
为moatless添加vLLM部署的模型配置
基于最新的vLLM OpenAI兼容API文档
"""

import asyncio
import argparse
import json
from pathlib import Path
from moatless.completion.manager import ModelConfigManager
from moatless.completion.base import BaseCompletionModel
from moatless.storage.file_storage import FileStorage


async def add_vllm_model(
    model_id: str,
    model_name: str,
    base_url: str = "http://localhost:8000",
    api_key: str = "token-abc123",
    temperature: float = 0.0,
    max_tokens: int = 4000,
    timeout: float = 120.0,
    completion_model_class: str = "moatless.completion.tool_call.ToolCallCompletionModel"
):
    """添加vLLM模型配置到moatless"""
    
    # 确保base_url格式正确
    if not base_url.endswith("/v1"):
        base_url = f"{base_url}/v1"
    
    storage = FileStorage(base_dir="./.moatless")
    manager = ModelConfigManager(storage=storage)
    await manager.initialize()
    
    # 创建vLLM模型配置
    vllm_config = BaseCompletionModel(
        model_id=model_id,
        model=model_name,  # 这应该是vLLM服务中的模型名称
        temperature=temperature,
        max_tokens=max_tokens,
        timeout=timeout,
        model_base_url=base_url,
        model_api_key=api_key,
        metadata=None,
        message_cache=True,
        thoughts_in_action=False,
        disable_thoughts=False,
        few_shot_examples=True,
        headers={},
        params={},
        merge_same_role_messages=False,
        completion_model_class=completion_model_class
    )
    
    try:
        await manager.create_model(vllm_config)
        print(f"✅ 成功添加vLLM模型配置: {model_id}")
        print(f"   模型名称: {model_name}")
        print(f"   API端点: {base_url}")
        print(f"   API密钥: {api_key}")
        print(f"   完成模型类: {completion_model_class}")
        
        # 测试模型配置
        print("\n🧪 测试模型配置...")
        test_result = await manager.test_model_config(model_id)
        if test_result.get("success"):
            print("✅ 模型测试成功!")
            print(f"   响应: {test_result.get('model_response', 'N/A')}")
        else:
            print("❌ 模型测试失败:")
            print(f"   错误: {test_result.get('message', 'Unknown error')}")
            
    except Exception as e:
        print(f"❌ 添加模型配置失败: {e}")


def main():
    parser = argparse.ArgumentParser(description="为moatless添加vLLM模型配置")
    parser.add_argument("--model-id", required=True, help="模型ID（用于moatless内部识别）")
    parser.add_argument("--model-name", required=True, help="vLLM服务中的模型名称")
    parser.add_argument("--base-url", default="http://localhost:8000", help="vLLM服务的基础URL")
    parser.add_argument("--api-key", default="token-abc123", help="API密钥")
    parser.add_argument("--temperature", type=float, default=0.0, help="温度参数")
    parser.add_argument("--max-tokens", type=int, default=4000, help="最大token数")
    parser.add_argument("--timeout", type=float, default=120.0, help="请求超时时间")
    parser.add_argument("--completion-model-class", 
                       default="moatless.completion.tool_call.ToolCallCompletionModel",
                       choices=[
                           "moatless.completion.tool_call.ToolCallCompletionModel",
                           "moatless.completion.react.ReActCompletionModel"
                       ],
                       help="完成模型类")
    
    args = parser.parse_args()
    
    asyncio.run(add_vllm_model(
        model_id=args.model_id,
        model_name=args.model_name,
        base_url=args.base_url,
        api_key=args.api_key,
        temperature=args.temperature,
        max_tokens=args.max_tokens,
        timeout=args.timeout,
        completion_model_class=args.completion_model_class
    ))


if __name__ == "__main__":
    main()
