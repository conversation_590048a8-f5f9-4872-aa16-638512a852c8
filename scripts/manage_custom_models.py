#!/usr/bin/env python3
"""
moatless自定义模型管理工具
支持vLLM、Ollama和其他OpenAI兼容的API服务
"""

import asyncio
import argparse
import json
import aiohttp
from pathlib import Path
from moatless.completion.manager import ModelConfigManager
from moatless.completion.base import BaseCompletionModel
from moatless.storage.file_storage import FileStorage


class CustomModelManager:
    def __init__(self, moatless_dir: str = "./.moatless"):
        self.moatless_dir = moatless_dir
        
    async def get_manager(self):
        storage = FileStorage(base_dir=self.moatless_dir)
        manager = ModelConfigManager(storage=storage)
        await manager.initialize()
        return manager
    
    async def add_model(self, config: dict):
        """添加自定义模型配置"""
        manager = await self.get_manager()
        
        model_config = BaseCompletionModel(
            model_id=config["model_id"],
            model=config["model"],
            temperature=config.get("temperature", 0.0),
            max_tokens=config.get("max_tokens", 4000),
            timeout=config.get("timeout", 120.0),
            model_base_url=config["model_base_url"],
            model_api_key=config.get("model_api_key", "EMPTY"),
            metadata=config.get("metadata"),
            message_cache=config.get("message_cache", True),
            thoughts_in_action=config.get("thoughts_in_action", False),
            disable_thoughts=config.get("disable_thoughts", False),
            few_shot_examples=config.get("few_shot_examples", True),
            headers=config.get("headers", {}),
            params=config.get("params", {}),
            merge_same_role_messages=config.get("merge_same_role_messages", False),
            completion_model_class=config.get("completion_model_class", 
                                             "moatless.completion.tool_call.ToolCallCompletionModel")
        )
        
        try:
            await manager.create_model(model_config)
            print(f"✅ 成功添加模型配置: {config['model_id']}")
            return True
        except Exception as e:
            print(f"❌ 添加模型配置失败: {e}")
            return False
    
    async def test_model(self, model_id: str):
        """测试模型配置"""
        manager = await self.get_manager()
        try:
            result = await manager.test_model_config(model_id)
            if result.get("success"):
                print(f"✅ 模型 {model_id} 测试成功!")
                print(f"   响应: {result.get('model_response', 'N/A')}")
            else:
                print(f"❌ 模型 {model_id} 测试失败:")
                print(f"   错误: {result.get('message', 'Unknown error')}")
            return result.get("success", False)
        except Exception as e:
            print(f"❌ 测试模型失败: {e}")
            return False
    
    async def list_models(self):
        """列出所有模型配置"""
        manager = await self.get_manager()
        try:
            configs = manager.get_all_configs()
            print("📋 当前模型配置:")
            for config in configs:
                print(f"   - {config.model_id}: {config.model} ({config.model_base_url or 'default'})")
            return configs
        except Exception as e:
            print(f"❌ 获取模型列表失败: {e}")
            return []
    
    async def delete_model(self, model_id: str):
        """删除模型配置"""
        manager = await self.get_manager()
        try:
            await manager.delete_model_config(model_id)
            print(f"✅ 成功删除模型配置: {model_id}")
            return True
        except Exception as e:
            print(f"❌ 删除模型配置失败: {e}")
            return False
    
    async def check_service_health(self, base_url: str):
        """检查服务健康状态"""
        try:
            # 尝试访问健康检查端点
            health_urls = [
                f"{base_url}/health",
                f"{base_url}/v1/models",
                f"{base_url}/api/tags"  # Ollama
            ]
            
            async with aiohttp.ClientSession() as session:
                for url in health_urls:
                    try:
                        async with session.get(url, timeout=5) as response:
                            if response.status == 200:
                                print(f"✅ 服务健康: {base_url}")
                                return True
                    except:
                        continue
                        
            print(f"❌ 服务不可用: {base_url}")
            return False
        except Exception as e:
            print(f"❌ 检查服务健康状态失败: {e}")
            return False


def create_vllm_config(model_id: str, model_name: str, base_url: str = "http://localhost:8000", 
                      api_key: str = "token-abc123", **kwargs):
    """创建vLLM模型配置"""
    if not base_url.endswith("/v1"):
        base_url = f"{base_url}/v1"
    
    return {
        "model_id": model_id,
        "model": model_name,
        "model_base_url": base_url,
        "model_api_key": api_key,
        **kwargs
    }


def create_ollama_config(model_id: str, model_name: str, base_url: str = "http://localhost:11434", 
                        api_key: str = "ollama", **kwargs):
    """创建Ollama模型配置"""
    if not base_url.endswith("/v1"):
        base_url = f"{base_url}/v1"
    
    return {
        "model_id": model_id,
        "model": model_name,
        "model_base_url": base_url,
        "model_api_key": api_key,
        **kwargs
    }


async def main():
    parser = argparse.ArgumentParser(description="moatless自定义模型管理工具")
    parser.add_argument("--moatless-dir", default="./.moatless", help="moatless配置目录")
    
    subparsers = parser.add_subparsers(dest="command", help="可用命令")
    
    # 添加模型命令
    add_parser = subparsers.add_parser("add", help="添加模型配置")
    add_parser.add_argument("--type", choices=["vllm", "ollama", "custom"], required=True, help="模型类型")
    add_parser.add_argument("--model-id", required=True, help="模型ID")
    add_parser.add_argument("--model-name", required=True, help="模型名称")
    add_parser.add_argument("--base-url", help="API基础URL")
    add_parser.add_argument("--api-key", help="API密钥")
    add_parser.add_argument("--temperature", type=float, default=0.0, help="温度参数")
    add_parser.add_argument("--max-tokens", type=int, default=4000, help="最大token数")
    
    # 测试模型命令
    test_parser = subparsers.add_parser("test", help="测试模型配置")
    test_parser.add_argument("model_id", help="要测试的模型ID")
    
    # 列出模型命令
    subparsers.add_parser("list", help="列出所有模型配置")
    
    # 删除模型命令
    delete_parser = subparsers.add_parser("delete", help="删除模型配置")
    delete_parser.add_argument("model_id", help="要删除的模型ID")
    
    # 检查服务命令
    health_parser = subparsers.add_parser("health", help="检查服务健康状态")
    health_parser.add_argument("base_url", help="服务基础URL")
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    manager = CustomModelManager(args.moatless_dir)
    
    if args.command == "add":
        if args.type == "vllm":
            config = create_vllm_config(
                args.model_id, args.model_name,
                args.base_url or "http://localhost:8000",
                args.api_key or "token-abc123",
                temperature=args.temperature,
                max_tokens=args.max_tokens
            )
        elif args.type == "ollama":
            config = create_ollama_config(
                args.model_id, args.model_name,
                args.base_url or "http://localhost:11434",
                args.api_key or "ollama",
                temperature=args.temperature,
                max_tokens=args.max_tokens
            )
        else:  # custom
            if not args.base_url:
                print("❌ 自定义类型需要提供 --base-url")
                return
            config = {
                "model_id": args.model_id,
                "model": args.model_name,
                "model_base_url": args.base_url,
                "model_api_key": args.api_key or "EMPTY",
                "temperature": args.temperature,
                "max_tokens": args.max_tokens
            }
        
        success = await manager.add_model(config)
        if success:
            await manager.test_model(args.model_id)
    
    elif args.command == "test":
        await manager.test_model(args.model_id)
    
    elif args.command == "list":
        await manager.list_models()
    
    elif args.command == "delete":
        await manager.delete_model(args.model_id)
    
    elif args.command == "health":
        await manager.check_service_health(args.base_url)


if __name__ == "__main__":
    asyncio.run(main())
