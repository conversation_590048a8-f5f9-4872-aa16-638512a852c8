#!/usr/bin/env python3
"""
测试自定义模型配置的简单脚本
"""

import asyncio
import argparse
from moatless.completion.manager import ModelConfigManager
from moatless.storage.file_storage import FileStorage
from moatless.actions import Respond
from moatless.agent import ActionAgent


async def test_model_with_agent(model_id: str, message: str = "你好，请介绍一下你自己。"):
    """使用ActionAgent测试模型"""
    
    storage = FileStorage(base_dir="./.moatless")
    model_config_manager = ModelConfigManager(storage=storage)
    await model_config_manager.initialize()
    
    try:
        # 获取模型配置
        completion_model = model_config_manager.create_completion_model(model_id)
        
        # 创建代理
        agent = ActionAgent(
            completion_model=completion_model,
            system_prompt="你是一个有用的AI助手，可以回答问题并提供帮助。请用中文回答。",
            actions=[Respond()],
        )
        
        print(f"🤖 使用模型 {model_id} 进行对话测试...")
        print(f"👤 用户: {message}")
        
        # 发送消息并获取响应
        response = await agent.run(message)
        
        print(f"🤖 助手: {response}")
        print("✅ 模型测试成功!")
        
    except Exception as e:
        print(f"❌ 模型测试失败: {e}")


async def main():
    parser = argparse.ArgumentParser(description="测试自定义模型配置")
    parser.add_argument("model_id", help="要测试的模型ID")
    parser.add_argument("--message", default="你好，请介绍一下你自己。", help="测试消息")
    
    args = parser.parse_args()
    
    await test_model_with_agent(args.model_id, args.message)


if __name__ == "__main__":
    asyncio.run(main())
