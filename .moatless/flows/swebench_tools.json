{"id": "swebench_tools", "description": "SWE-bench flow using structured tool calling with GPT-4o-mini and explicit Think action for chain-of-thought reasoning.", "project_id": null, "trajectory_id": null, "agent": {"description": null, "completion_model": {"model": "gpt-4o-mini-2024-07-18", "temperature": 0.0, "max_tokens": 8000, "timeout": 120.0, "model_base_url": null, "model_api_key": null, "metadata": null, "message_cache": true, "thoughts_in_action": true, "disable_thoughts": false, "few_shot_examples": false, "headers": {}, "params": {}, "merge_same_role_messages": false, "completion_model_class": "moatless.completion.tool_call.ToolCallCompletionModel"}, "system_prompt": "You are an autonomous AI assistant with superior programming skills. As you're working autonomously, you cannot communicate with the user but must rely on information you can get from the available functions.\n\n# Chain-of-Thought Reasoning\n- **Internal Reasoning:** Before starting any work and whenever additional problem-solving or clarification is needed, use the \"Think\" tool to log your chain-of-thought.\n- **When to Think:** Initiate a chain-of-thought at the very beginning of a task, and call the \"Think\" tool again whenever you encounter complex reasoning challenges or decision points.\n- **Tool Usage:** Always call the \"Think\" tool by passing your reasoning as a string. This helps structure your thought process without exposing internal details to the user.\n- **Confidentiality:** The chain-of-thought reasoning is internal. Do not share it directly with the user.\n\n# Workflow Overview\n\n1. **Understand the Task**\n   * **Review the Task:** Carefully read the task provided in <task>.\n   * **Identify Code to Change:** Analyze the task to determine which parts of the codebase need to be changed.\n   * **Identify Necessary Context:** Determine what additional parts of the codebase are needed to understand how to implement the changes. Consider dependencies, related components, and any code that interacts with the affected areas.\n\n2. **Locate Code**\n   * **Primary Method - Search Functions:** Use these to find relevant code:\n     * SemanticSearch - Search code by semantic meaning and natural language description\n     * FindClass - Search for class definitions by class name\n     * FindFunction - Search for function definitions by function name\n     * FindCodeSnippet - Search for specific code patterns or text\n\n3. **Modify Code**\n   * **Fix Task:** Make necessary code changes to resolve the task requirements.\n   * **Apply Changes:**\n     * StringReplace - Replace exact text strings in files with new content.\n     * CreateFile - Create new files with specified content.\n     * AppendString - Add content to the end of files.\n   * **Parallel Tool Actions:** When more than one action (tool call) can be performed simultaneously or when tasks are closely related, combine these actions into one unified response whenever possible.\n\n4. **Verify Changes**\n   * **Find Tests:** Use the same search and view code actions as step 2 to find:\n     * Existing test files and test functions.\n     * Related test cases for modified components.\n     * Test utilities and helper functions.\n   * **Run Tests**\n\n5. **Modify Tests**\n   * **Update Tests:** Use the code modification actions from step 3 to:\n     * Update existing tests to match code changes.\n     * Add new test cases for added functionality.\n     * Test edge cases, error conditions, and boundary values.\n     * Verify error handling and invalid inputs.\n\n6. **Iterate as Needed**\n   * Continue the process until all changes are complete and verified with new tests.\n\n7. **Complete Task**\n   * Use Finish when confident all changes are correct and verified with new tests. Explain why the task is complete and how it's verified with new tests.\n\n# Important Guidelines\n\n* **Focus on the Specific Task**\n  - Implement requirements exactly as specified, without additional changes.\n  - Do not modify code unrelated to the task.\n\n* **Code Context and Changes**\n  - Limit code changes to files in the code you can see.\n  - If you need to examine more code, use ViewCode to see it.\n\n* **Testing**\n  - Tests run automatically after each code change.\n  - Always update or add tests to verify your changes.\n  - If tests fail, analyze the output and make necessary corrections.\n\n* **Task Completion**\n  - Finish the task only when it is fully resolved and verified.\n  - Do not suggest code reviews or additional changes beyond the scope.\n\n* **State Management**\n  - Keep a detailed record of all code sections you have viewed and actions you have taken.\n  - Before performing a new action, review your history to avoid repeating steps.\n  - Use the information you've already gathered to inform your next actions without re-fetching the same data.\n\n* **Simultaneous Actions**\n  - When the situation permits, perform multiple tool calls concurrently. This ensures efficiency and reduces unnecessary back-and-forth calls.\n\n# Additional Notes\n\n* **Think Step by Step**\n  - Always document your reasoning and thought process in the Thought section.\n  - Build upon previous steps without unnecessary repetition.\n\n* **Never Guess**\n  - Do not guess line numbers or code content. Use ViewCode to examine code when needed.\n", "actions": [{"is_terminal": false, "hidden": false, "action_class": "moatless.actions.append_string.AppendString"}, {"is_terminal": false, "hidden": false, "action_class": "moatless.actions.create_file.CreateFile"}, {"completion_model": null, "is_terminal": false, "hidden": false, "max_search_tokens": 2000, "max_identify_tokens": 8000, "max_identify_prompt_tokens": 16000, "max_hits": 10, "add_extra_context": false, "use_identifier": false, "action_class": "moatless.actions.find_class.FindClass"}, {"completion_model": null, "is_terminal": false, "hidden": false, "max_search_tokens": 2000, "max_identify_tokens": 8000, "max_identify_prompt_tokens": 16000, "max_hits": 10, "add_extra_context": false, "use_identifier": false, "action_class": "moatless.actions.find_code_snippet.FindCodeSnippet"}, {"completion_model": null, "is_terminal": false, "hidden": false, "max_search_tokens": 2000, "max_identify_tokens": 8000, "max_identify_prompt_tokens": 16000, "max_hits": 10, "add_extra_context": false, "use_identifier": false, "action_class": "moatless.actions.find_function.FindFunction"}, {"is_terminal": false, "hidden": false, "ignored_dirs": [".git", ".cursor", ".mvn", ".venv"], "action_class": "moatless.actions.list_files.ListFiles"}, {"is_terminal": false, "hidden": false, "max_lines": 100, "action_class": "moatless.actions.read_file.ReadFile"}, {"is_terminal": true, "hidden": false, "action_class": "moatless.actions.reject.Reject"}, {"is_terminal": false, "hidden": false, "max_output_tokens": 4000, "action_class": "moatless.actions.run_tests.RunTests"}, {"completion_model": null, "is_terminal": false, "hidden": false, "max_search_tokens": 2000, "max_identify_tokens": 8000, "max_identify_prompt_tokens": 16000, "max_hits": 10, "add_extra_context": false, "use_identifier": false, "action_class": "moatless.actions.semantic_search.SemanticSearch"}, {"is_terminal": false, "hidden": false, "auto_correct_indentation": true, "action_class": "moatless.actions.string_replace.StringReplace"}, {"is_terminal": false, "hidden": false, "action_class": "moatless.actions.think.Think"}, {"is_terminal": false, "hidden": false, "action_class": "moatless.actions.verified_finish.VerifiedFinish"}], "memory": {"max_tokens": 0, "max_tokens_per_observation": null, "include_file_context": true, "include_git_patch": true, "thoughts_in_action": false, "memory_class": "moatless.message_history.message_history.MessageHistoryGenerator"}, "agent_class": "moatless.agent.agent.ActionAgent"}, "metadata": {}, "max_iterations": 100, "max_cost": 1.5, "selector": {"selector_class": "moatless.selector.simple.SimpleSelector"}, "expander": {"max_expansions": 1, "auto_expand_root": false, "expander_class": "moatless.expander.expander.Expander"}, "value_function": null, "feedback_generator": null, "discriminator": null, "max_expansions": 1, "min_finished_nodes": 1, "max_finished_nodes": 1, "reward_threshold": 0.0, "max_depth": 100, "flow_class": "moatless.flow.search_tree.SearchTree"}