[{"model_id": "claude-sonnet-4-20250514", "model": "claude-sonnet-4-20250514", "temperature": 0.0, "max_tokens": 4000, "timeout": 120.0, "model_base_url": null, "model_api_key": null, "metadata": null, "message_cache": true, "thoughts_in_action": false, "disable_thoughts": false, "few_shot_examples": true, "headers": {}, "params": {}, "merge_same_role_messages": false, "completion_model_class": "moatless.completion.tool_call.ToolCallCompletionModel"}, {"model_id": "claude-sonnet-4-20250514-thinking", "model": "claude-sonnet-4-20250514", "temperature": 1.0, "max_tokens": 16000, "timeout": 120.0, "model_base_url": null, "model_api_key": null, "metadata": null, "message_cache": true, "thoughts_in_action": false, "disable_thoughts": false, "few_shot_examples": false, "headers": {"anthropic-beta": "interleaved-thinking-2025-05-14"}, "params": {"thinking": {"type": "enabled", "budget_tokens": 10000}}, "merge_same_role_messages": false, "completion_model_class": "moatless.completion.tool_call.ToolCallCompletionModel"}, {"model_id": "deepseek-chat", "model": "deepseek/deepseek-chat", "temperature": 0.0, "max_tokens": 4000, "timeout": 120.0, "model_base_url": null, "model_api_key": "", "metadata": null, "message_cache": true, "thoughts_in_action": false, "disable_thoughts": false, "few_shot_examples": true, "headers": {}, "params": {}, "merge_same_role_messages": false, "max_actions": null, "completion_model_class": "moatless.completion.react.ReActCompletionModel"}, {"model_id": "gpt-4o-mini-2024-07-18", "model": "gpt-4o-mini-2024-07-18", "temperature": 0.0, "max_tokens": 8000, "timeout": 120.0, "model_base_url": null, "model_api_key": null, "metadata": null, "message_cache": true, "thoughts_in_action": true, "disable_thoughts": false, "few_shot_examples": true, "headers": {}, "params": {}, "merge_same_role_messages": false, "completion_model_class": "moatless.completion.tool_call.ToolCallCompletionModel"}, {"model_id": "qwen-2.5-coder-32b-instruct", "model": "openrouter/qwen/qwen-2.5-coder-32b-instruct", "temperature": 0.0, "max_tokens": 4000, "timeout": 120.0, "model_base_url": null, "model_api_key": null, "metadata": null, "message_cache": true, "thoughts_in_action": false, "disable_thoughts": false, "few_shot_examples": true, "headers": {}, "params": {}, "merge_same_role_messages": false, "max_actions": null, "completion_model_class": "moatless.completion.react.ReActCompletionModel"}, {"model_id": "openrouter-deepseek-v3", "model": "openrouter/deepseek/deepseek-chat-v3-0324", "temperature": 0.7, "max_tokens": 4000, "timeout": 120.0, "model_base_url": null, "model_api_key": "", "metadata": null, "message_cache": true, "thoughts_in_action": false, "disable_thoughts": false, "few_shot_examples": true, "headers": {}, "params": {}, "merge_same_role_messages": false, "max_actions": null, "completion_model_class": "moatless.completion.react.ReActCompletionModel"}, {"model_id": "claude-3-5-haiku-20241022", "model": "claude-3-5-haiku-20241022", "temperature": 0.0, "max_tokens": 4000, "timeout": 120.0, "model_base_url": null, "model_api_key": null, "metadata": null, "message_cache": true, "thoughts_in_action": false, "disable_thoughts": false, "few_shot_examples": true, "headers": {}, "params": {}, "merge_same_role_messages": false, "completion_model_class": "moatless.completion.tool_call.ToolCallCompletionModel"}, {"model_id": "gpt-4.1-mini-2025-04-14", "model": "gpt-4.1-mini-2025-04-14", "temperature": 1.0, "max_tokens": 4096, "timeout": 120.0, "model_base_url": "", "model_api_key": "", "metadata": null, "message_cache": true, "thoughts_in_action": false, "disable_thoughts": false, "few_shot_examples": false, "headers": {}, "params": {}, "merge_same_role_messages": false, "completion_model_class": "moatless.completion.tool_call.ToolCallCompletionModel"}, {"model_id": "deepseek-r1-0528", "model": "openrouter/deepseek/deepseek-r1-0528", "temperature": 0.7, "max_tokens": 4096, "timeout": 120.0, "model_base_url": "", "model_api_key": "", "metadata": null, "message_cache": true, "thoughts_in_action": false, "disable_thoughts": false, "few_shot_examples": true, "headers": {}, "params": {}, "merge_same_role_messages": false, "max_actions": null, "completion_model_class": "moatless.completion.react.ReActCompletionModel"}, {"model_id": "devstral-small", "model": "openrouter/mistralai/devstral-small", "temperature": 0.0, "max_tokens": 4096, "timeout": 120.0, "model_base_url": "", "model_api_key": "", "metadata": null, "message_cache": true, "thoughts_in_action": false, "disable_thoughts": false, "few_shot_examples": true, "headers": {}, "params": {}, "merge_same_role_messages": false, "max_actions": null, "completion_model_class": "moatless.completion.react.ReActCompletionModel"}, {"model_id": "gemini-2.5-flash-preview-05-20", "model": "gemini/gemini-2.5-flash-preview-05-20", "temperature": 0.0, "max_tokens": 4096, "timeout": 120.0, "model_base_url": "", "model_api_key": "", "metadata": null, "message_cache": true, "thoughts_in_action": false, "disable_thoughts": false, "few_shot_examples": false, "headers": {}, "params": {}, "merge_same_role_messages": false, "completion_model_class": "moatless.completion.tool_call.ToolCallCompletionModel"}]