>>>>> Run tests
<frozen importlib._bootstrap>:228: RuntimeWarning: numpy.ndarray size changed, may indicate binary incompatibility. Expected 80 from C header, got 96 from PyObject
============================= test session starts ==============================
platform linux -- Python 3.9.19, pytest-7.4.0, pluggy-1.3.0

Running tests with Astropy version 5.1.dev623+gd16bfe05a7.d20240715.
Running tests in astropy/modeling/tests/test_separable.py.

Date: 2024-08-27T06:15:54

Platform: Linux-5.15.0-1070-azure-x86_64-with-glibc2.35

Executable: /opt/miniconda3/envs/testbed/bin/python

Full Python Version: 
3.9.19 (main, May  6 2024, 19:43:03) 
[GCC 11.2.0]

encodings: sys: utf-8, locale: UTF-8, filesystem: utf-8
byteorder: little
float info: dig: 15, mant_dig: 15

Package versions: 
Numpy: 1.25.2
Scipy: not available
Matplotlib: not available
h5py: not available
Pandas: not available
PyERFA: *******
Cython: not available
Scikit-image: not available
asdf: not available
pyarrow: not available

Using Astropy options: remote_data: none.

ARCH_ON_CI: undefined
IS_CRON: undefined

rootdir: /testbed
configfile: setup.cfg
plugins: astropy-header-0.2.2, openfiles-0.5.0, remotedata-0.4.0, filter-subpackage-0.1.2, xdist-3.3.1, doctestplus-1.0.0, mock-3.11.1, cov-4.1.0, astropy-0.10.0, hypothesis-6.82.6, arraydiff-0.5.0
collected 11 items

astropy/modeling/tests/test_separable.py ....FFFF.F.                     [100%]

=================================== FAILURES ===================================
___________________ test_separable[compound_model0-result0] ____________________

compound_model = <CompoundModel(offset_1=1., angle_2=2., offset_3=1., offset_4=1., offset_5=2., offset_6=1.)>
result = (array([False, False,  True]), array([[ True, False],
       [ True, False],
       [False,  True]]))

    @pytest.mark.parametrize(('compound_model', 'result'), compound_models.values())
    def test_separable(compound_model, result):
>       assert_allclose(is_separable(compound_model), result[0])

astropy/modeling/tests/test_separable.py:134: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

transform = <CompoundModel(offset_1=1., angle_2=2., offset_3=1., offset_4=1., offset_5=2., offset_6=1.)>

    def is_separable(transform):
        """
        A separability test for the outputs of a transform.
    
        Parameters
        ----------
        transform : `~astropy.modeling.core.Model`
            A (compound) model.
    
        Returns
        -------
        is_separable : ndarray
            A boolean array with size ``transform.n_outputs`` where
            each element indicates whether the output is independent
            and the result of a separable transform.
    
        Examples
        --------
        >>> from astropy.modeling.models import Shift, Scale, Rotation2D, Polynomial2D
        >>> is_separable(Shift(1) & Shift(2) | Scale(1) & Scale(2))
            array([ True,  True]...)
        >>> is_separable(Shift(1) & Shift(2) | Rotation2D(2))
            array([False, False]...)
        >>> is_separable(Shift(1) & Shift(2) | Mapping([0, 1, 0, 1]) | \
            Polynomial2D(1) & Polynomial2D(2))
            array([False, False]...)
        >>> is_separable(Shift(1) & Shift(2) | Mapping([0, 1, 0, 1]))
            array([ True,  True,  True,  True]...)
    
        """
        if transform.n_inputs == 1 and transform.n_outputs > 1:
            is_separable = np.array([False] * transform.n_outputs).T
            return is_separable
>       separable_matrix = _separable(transform)

astropy/modeling/separable.py:60: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

transform = <CompoundModel(offset_1=1., angle_2=2., offset_3=1., offset_4=1., offset_5=2., offset_6=1.)>

    def _separable(transform):
        """
        Calculate the separability of outputs.
    
        Parameters
        ----------
        transform : `astropy.modeling.Model`
            A transform (usually a compound model).
    
        Returns :
        is_separable : ndarray of dtype np.bool
            An array of shape (transform.n_outputs,) of boolean type
            Each element represents the separablity of the corresponding output.
        """
        if (transform_matrix := transform._calculate_separability_matrix()) is not NotImplemented:
            return transform_matrix
        elif isinstance(transform, CompoundModel):
            sepleft = _separable(transform.left)
>           sepright = _separable(transform.right)

astropy/modeling/separable.py:308: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

transform = <CompoundModel(offset_0=1., offset_1=2., offset_2=1.)>

    def _separable(transform):
        """
        Calculate the separability of outputs.
    
        Parameters
        ----------
        transform : `astropy.modeling.Model`
            A transform (usually a compound model).
    
        Returns :
        is_separable : ndarray of dtype np.bool
            An array of shape (transform.n_outputs,) of boolean type
            Each element represents the separablity of the corresponding output.
        """
        if (transform_matrix := transform._calculate_separability_matrix()) is not NotImplemented:
            return transform_matrix
        elif isinstance(transform, CompoundModel):
            sepleft = _separable(transform.left)
            sepright = _separable(transform.right)
            if isinstance(transform.left, CompoundModel) or isinstance(transform.right, CompoundModel):
>               return np.block([[sepleft, np.zeros_like(sepright)],
                                 [np.zeros_like(sepleft), sepright]])

astropy/modeling/separable.py:310: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

arrays = [[array([[1., 0.],
       [0., 1.]]), array([[0.]])], [array([[0., 0.],
       [0., 0.]]), array([[1.]])]]

    @array_function_dispatch(_block_dispatcher)
    def block(arrays):
        """
        Assemble an nd-array from nested lists of blocks.
    
        Blocks in the innermost lists are concatenated (see `concatenate`) along
        the last dimension (-1), then these are concatenated along the
        second-last dimension (-2), and so on until the outermost list is reached.
    
        Blocks can be of any dimension, but will not be broadcasted using the normal
        rules. Instead, leading axes of size 1 are inserted, to make ``block.ndim``
        the same for all blocks. This is primarily useful for working with scalars,
        and means that code like ``np.block([v, 1])`` is valid, where
        ``v.ndim == 1``.
    
        When the nested list is two levels deep, this allows block matrices to be
        constructed from their components.
    
        .. versionadded:: 1.13.0
    
        Parameters
        ----------
        arrays : nested list of array_like or scalars (but not tuples)
            If passed a single ndarray or scalar (a nested list of depth 0), this
            is returned unmodified (and not copied).
    
            Elements shapes must match along the appropriate axes (without
            broadcasting), but leading 1s will be prepended to the shape as
            necessary to make the dimensions match.
    
        Returns
        -------
        block_array : ndarray
            The array assembled from the given blocks.
    
            The dimensionality of the output is equal to the greatest of:
            * the dimensionality of all the inputs
            * the depth to which the input list is nested
    
        Raises
        ------
        ValueError
            * If list depths are mismatched - for instance, ``[[a, b], c]`` is
              illegal, and should be spelt ``[[a, b], [c]]``
            * If lists are empty - for instance, ``[[a, b], []]``
    
        See Also
        --------
        concatenate : Join a sequence of arrays along an existing axis.
        stack : Join a sequence of arrays along a new axis.
        vstack : Stack arrays in sequence vertically (row wise).
        hstack : Stack arrays in sequence horizontally (column wise).
        dstack : Stack arrays in sequence depth wise (along third axis).
        column_stack : Stack 1-D arrays as columns into a 2-D array.
        vsplit : Split an array into multiple sub-arrays vertically (row-wise).
    
        Notes
        -----
    
        When called with only scalars, ``np.block`` is equivalent to an ndarray
        call. So ``np.block([[1, 2], [3, 4]])`` is equivalent to
        ``np.array([[1, 2], [3, 4]])``.
    
        This function does not enforce that the blocks lie on a fixed grid.
        ``np.block([[a, b], [c, d]])`` is not restricted to arrays of the form::
    
            AAAbb
            AAAbb
            cccDD
    
        But is also allowed to produce, for some ``a, b, c, d``::
    
            AAAbb
            AAAbb
            cDDDD
    
        Since concatenation happens along the last axis first, `block` is _not_
        capable of producing the following directly::
    
            AAAbb
            cccbb
            cccDD
    
        Matlab's "square bracket stacking", ``[A, B, ...; p, q, ...]``, is
        equivalent to ``np.block([[A, B, ...], [p, q, ...]])``.
    
        Examples
        --------
        The most common use of this function is to build a block matrix
    
        >>> A = np.eye(2) * 2
        >>> B = np.eye(3) * 3
        >>> np.block([
        ...     [A,               np.zeros((2, 3))],
        ...     [np.ones((3, 2)), B               ]
        ... ])
        array([[2., 0., 0., 0., 0.],
               [0., 2., 0., 0., 0.],
               [1., 1., 3., 0., 0.],
               [1., 1., 0., 3., 0.],
               [1., 1., 0., 0., 3.]])
    
        With a list of depth 1, `block` can be used as `hstack`
    
        >>> np.block([1, 2, 3])              # hstack([1, 2, 3])
        array([1, 2, 3])
    
        >>> a = np.array([1, 2, 3])
        >>> b = np.array([4, 5, 6])
        >>> np.block([a, b, 10])             # hstack([a, b, 10])
        array([ 1,  2,  3,  4,  5,  6, 10])
    
        >>> A = np.ones((2, 2), int)
        >>> B = 2 * A
        >>> np.block([A, B])                 # hstack([A, B])
        array([[1, 1, 2, 2],
               [1, 1, 2, 2]])
    
        With a list of depth 2, `block` can be used in place of `vstack`:
    
        >>> a = np.array([1, 2, 3])
        >>> b = np.array([4, 5, 6])
        >>> np.block([[a], [b]])             # vstack([a, b])
        array([[1, 2, 3],
               [4, 5, 6]])
    
        >>> A = np.ones((2, 2), int)
        >>> B = 2 * A
        >>> np.block([[A], [B]])             # vstack([A, B])
        array([[1, 1],
               [1, 1],
               [2, 2],
               [2, 2]])
    
        It can also be used in places of `atleast_1d` and `atleast_2d`
    
        >>> a = np.array(0)
        >>> b = np.array([1])
        >>> np.block([a])                    # atleast_1d(a)
        array([0])
        >>> np.block([b])                    # atleast_1d(b)
        array([1])
    
        >>> np.block([[a]])                  # atleast_2d(a)
        array([[0]])
        >>> np.block([[b]])                  # atleast_2d(b)
        array([[1]])
    
    
        """
        arrays, list_ndim, result_ndim, final_size = _block_setup(arrays)
    
        # It was found through benchmarking that making an array of final size
        # around 256x256 was faster by straight concatenation on a
        # i7-7700HQ processor and dual channel ram 2400MHz.
        # It didn't seem to matter heavily on the dtype used.
        #
        # A 2D array using repeated concatenation requires 2 copies of the array.
        #
        # The fastest algorithm will depend on the ratio of CPU power to memory
        # speed.
        # One can monitor the results of the benchmark
        # https://pv.github.io/numpy-bench/#bench_shape_base.Block2D.time_block2d
        # to tune this parameter until a C version of the `_block_info_recursion`
        # algorithm is implemented which would likely be faster than the python
        # version.
        if list_ndim * final_size > (2 * 512 * 512):
            return _block_slicing(arrays, list_ndim, result_ndim)
        else:
>           return _block_concatenate(arrays, list_ndim, result_ndim)

/opt/miniconda3/envs/testbed/lib/python3.9/site-packages/numpy/core/shape_base.py:872: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

arrays = [[array([[1., 0.],
       [0., 1.]]), array([[0.]])], [array([[0., 0.],
       [0., 0.]]), array([[1.]])]]
list_ndim = 2, result_ndim = 2

    def _block_concatenate(arrays, list_ndim, result_ndim):
>       result = _block(arrays, list_ndim, result_ndim)

/opt/miniconda3/envs/testbed/lib/python3.9/site-packages/numpy/core/shape_base.py:916: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

arrays = [[array([[1., 0.],
       [0., 1.]]), array([[0.]])], [array([[0., 0.],
       [0., 0.]]), array([[1.]])]]
max_depth = 2, result_ndim = 2, depth = 0

    def _block(arrays, max_depth, result_ndim, depth=0):
        """
        Internal implementation of block based on repeated concatenation.
        `arrays` is the argument passed to
        block. `max_depth` is the depth of nested lists within `arrays` and
        `result_ndim` is the greatest of the dimensions of the arrays in
        `arrays` and the depth of the lists in `arrays` (see block docstring
        for details).
        """
        if depth < max_depth:
>           arrs = [_block(arr, max_depth, result_ndim, depth+1)
                    for arr in arrays]

/opt/miniconda3/envs/testbed/lib/python3.9/site-packages/numpy/core/shape_base.py:683: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

.0 = <list_iterator object at 0x7fbd1b17f190>

>   arrs = [_block(arr, max_depth, result_ndim, depth+1)
            for arr in arrays]

/opt/miniconda3/envs/testbed/lib/python3.9/site-packages/numpy/core/shape_base.py:683: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

arrays = [array([[1., 0.],
       [0., 1.]]), array([[0.]])], max_depth = 2
result_ndim = 2, depth = 1

    def _block(arrays, max_depth, result_ndim, depth=0):
        """
        Internal implementation of block based on repeated concatenation.
        `arrays` is the argument passed to
        block. `max_depth` is the depth of nested lists within `arrays` and
        `result_ndim` is the greatest of the dimensions of the arrays in
        `arrays` and the depth of the lists in `arrays` (see block docstring
        for details).
        """
        if depth < max_depth:
            arrs = [_block(arr, max_depth, result_ndim, depth+1)
                    for arr in arrays]
>           return _concatenate(arrs, axis=-(max_depth-depth))
E           ValueError: all the input array dimensions except for the concatenation axis must match exactly, but along dimension 0, the array at index 0 has size 2 and the array at index 1 has size 1

/opt/miniconda3/envs/testbed/lib/python3.9/site-packages/numpy/core/shape_base.py:685: ValueError
___________________ test_separable[compound_model1-result1] ____________________

compound_model = <CompoundModel(offset_0=1., offset_1=2., angle_2=2., c0_0_4=0., c1_0_4=0., c0_1_4=0., c0_0_5=0., c1_0_5=0., c2_0_5=0., c0_1_5=0., c0_2_5=0., c1_1_5=0.)>
result = (array([False, False]), array([[ True,  True],
       [ True,  True]]))

    @pytest.mark.parametrize(('compound_model', 'result'), compound_models.values())
    def test_separable(compound_model, result):
>       assert_allclose(is_separable(compound_model), result[0])

astropy/modeling/tests/test_separable.py:134: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

transform = <CompoundModel(offset_0=1., offset_1=2., angle_2=2., c0_0_4=0., c1_0_4=0., c0_1_4=0., c0_0_5=0., c1_0_5=0., c2_0_5=0., c0_1_5=0., c0_2_5=0., c1_1_5=0.)>

    def is_separable(transform):
        """
        A separability test for the outputs of a transform.
    
        Parameters
        ----------
        transform : `~astropy.modeling.core.Model`
            A (compound) model.
    
        Returns
        -------
        is_separable : ndarray
            A boolean array with size ``transform.n_outputs`` where
            each element indicates whether the output is independent
            and the result of a separable transform.
    
        Examples
        --------
        >>> from astropy.modeling.models import Shift, Scale, Rotation2D, Polynomial2D
        >>> is_separable(Shift(1) & Shift(2) | Scale(1) & Scale(2))
            array([ True,  True]...)
        >>> is_separable(Shift(1) & Shift(2) | Rotation2D(2))
            array([False, False]...)
        >>> is_separable(Shift(1) & Shift(2) | Mapping([0, 1, 0, 1]) | \
            Polynomial2D(1) & Polynomial2D(2))
            array([False, False]...)
        >>> is_separable(Shift(1) & Shift(2) | Mapping([0, 1, 0, 1]))
            array([ True,  True,  True,  True]...)
    
        """
        if transform.n_inputs == 1 and transform.n_outputs > 1:
            is_separable = np.array([False] * transform.n_outputs).T
            return is_separable
>       separable_matrix = _separable(transform)

astropy/modeling/separable.py:60: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

transform = <CompoundModel(offset_0=1., offset_1=2., angle_2=2., c0_0_4=0., c1_0_4=0., c0_1_4=0., c0_0_5=0., c1_0_5=0., c2_0_5=0., c0_1_5=0., c0_2_5=0., c1_1_5=0.)>

    def _separable(transform):
        """
        Calculate the separability of outputs.
    
        Parameters
        ----------
        transform : `astropy.modeling.Model`
            A transform (usually a compound model).
    
        Returns :
        is_separable : ndarray of dtype np.bool
            An array of shape (transform.n_outputs,) of boolean type
            Each element represents the separablity of the corresponding output.
        """
        if (transform_matrix := transform._calculate_separability_matrix()) is not NotImplemented:
            return transform_matrix
        elif isinstance(transform, CompoundModel):
            sepleft = _separable(transform.left)
            sepright = _separable(transform.right)
            if isinstance(transform.left, CompoundModel) or isinstance(transform.right, CompoundModel):
>               return np.block([[sepleft, np.zeros_like(sepright)],
                                 [np.zeros_like(sepleft), sepright]])

astropy/modeling/separable.py:310: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

arrays = [[array([[1., 0., 0., 0., 0., 0.],
       [0., 1., 0., 0., 0., 0.],
       [0., 0., 1., 1., 0., 0.],
       [0., 0., 1...      [0., 0., 0., 0., 0., 0.],
       [0., 0., 0., 0., 0., 0.]]), array([[1., 1., 0., 0.],
       [0., 0., 1., 1.]])]]

    @array_function_dispatch(_block_dispatcher)
    def block(arrays):
        """
        Assemble an nd-array from nested lists of blocks.
    
        Blocks in the innermost lists are concatenated (see `concatenate`) along
        the last dimension (-1), then these are concatenated along the
        second-last dimension (-2), and so on until the outermost list is reached.
    
        Blocks can be of any dimension, but will not be broadcasted using the normal
        rules. Instead, leading axes of size 1 are inserted, to make ``block.ndim``
        the same for all blocks. This is primarily useful for working with scalars,
        and means that code like ``np.block([v, 1])`` is valid, where
        ``v.ndim == 1``.
    
        When the nested list is two levels deep, this allows block matrices to be
        constructed from their components.
    
        .. versionadded:: 1.13.0
    
        Parameters
        ----------
        arrays : nested list of array_like or scalars (but not tuples)
            If passed a single ndarray or scalar (a nested list of depth 0), this
            is returned unmodified (and not copied).
    
            Elements shapes must match along the appropriate axes (without
            broadcasting), but leading 1s will be prepended to the shape as
            necessary to make the dimensions match.
    
        Returns
        -------
        block_array : ndarray
            The array assembled from the given blocks.
    
            The dimensionality of the output is equal to the greatest of:
            * the dimensionality of all the inputs
            * the depth to which the input list is nested
    
        Raises
        ------
        ValueError
            * If list depths are mismatched - for instance, ``[[a, b], c]`` is
              illegal, and should be spelt ``[[a, b], [c]]``
            * If lists are empty - for instance, ``[[a, b], []]``
    
        See Also
        --------
        concatenate : Join a sequence of arrays along an existing axis.
        stack : Join a sequence of arrays along a new axis.
        vstack : Stack arrays in sequence vertically (row wise).
        hstack : Stack arrays in sequence horizontally (column wise).
        dstack : Stack arrays in sequence depth wise (along third axis).
        column_stack : Stack 1-D arrays as columns into a 2-D array.
        vsplit : Split an array into multiple sub-arrays vertically (row-wise).
    
        Notes
        -----
    
        When called with only scalars, ``np.block`` is equivalent to an ndarray
        call. So ``np.block([[1, 2], [3, 4]])`` is equivalent to
        ``np.array([[1, 2], [3, 4]])``.
    
        This function does not enforce that the blocks lie on a fixed grid.
        ``np.block([[a, b], [c, d]])`` is not restricted to arrays of the form::
    
            AAAbb
            AAAbb
            cccDD
    
        But is also allowed to produce, for some ``a, b, c, d``::
    
            AAAbb
            AAAbb
            cDDDD
    
        Since concatenation happens along the last axis first, `block` is _not_
        capable of producing the following directly::
    
            AAAbb
            cccbb
            cccDD
    
        Matlab's "square bracket stacking", ``[A, B, ...; p, q, ...]``, is
        equivalent to ``np.block([[A, B, ...], [p, q, ...]])``.
    
        Examples
        --------
        The most common use of this function is to build a block matrix
    
        >>> A = np.eye(2) * 2
        >>> B = np.eye(3) * 3
        >>> np.block([
        ...     [A,               np.zeros((2, 3))],
        ...     [np.ones((3, 2)), B               ]
        ... ])
        array([[2., 0., 0., 0., 0.],
               [0., 2., 0., 0., 0.],
               [1., 1., 3., 0., 0.],
               [1., 1., 0., 3., 0.],
               [1., 1., 0., 0., 3.]])
    
        With a list of depth 1, `block` can be used as `hstack`
    
        >>> np.block([1, 2, 3])              # hstack([1, 2, 3])
        array([1, 2, 3])
    
        >>> a = np.array([1, 2, 3])
        >>> b = np.array([4, 5, 6])
        >>> np.block([a, b, 10])             # hstack([a, b, 10])
        array([ 1,  2,  3,  4,  5,  6, 10])
    
        >>> A = np.ones((2, 2), int)
        >>> B = 2 * A
        >>> np.block([A, B])                 # hstack([A, B])
        array([[1, 1, 2, 2],
               [1, 1, 2, 2]])
    
        With a list of depth 2, `block` can be used in place of `vstack`:
    
        >>> a = np.array([1, 2, 3])
        >>> b = np.array([4, 5, 6])
        >>> np.block([[a], [b]])             # vstack([a, b])
        array([[1, 2, 3],
               [4, 5, 6]])
    
        >>> A = np.ones((2, 2), int)
        >>> B = 2 * A
        >>> np.block([[A], [B]])             # vstack([A, B])
        array([[1, 1],
               [1, 1],
               [2, 2],
               [2, 2]])
    
        It can also be used in places of `atleast_1d` and `atleast_2d`
    
        >>> a = np.array(0)
        >>> b = np.array([1])
        >>> np.block([a])                    # atleast_1d(a)
        array([0])
        >>> np.block([b])                    # atleast_1d(b)
        array([1])
    
        >>> np.block([[a]])                  # atleast_2d(a)
        array([[0]])
        >>> np.block([[b]])                  # atleast_2d(b)
        array([[1]])
    
    
        """
        arrays, list_ndim, result_ndim, final_size = _block_setup(arrays)
    
        # It was found through benchmarking that making an array of final size
        # around 256x256 was faster by straight concatenation on a
        # i7-7700HQ processor and dual channel ram 2400MHz.
        # It didn't seem to matter heavily on the dtype used.
        #
        # A 2D array using repeated concatenation requires 2 copies of the array.
        #
        # The fastest algorithm will depend on the ratio of CPU power to memory
        # speed.
        # One can monitor the results of the benchmark
        # https://pv.github.io/numpy-bench/#bench_shape_base.Block2D.time_block2d
        # to tune this parameter until a C version of the `_block_info_recursion`
        # algorithm is implemented which would likely be faster than the python
        # version.
        if list_ndim * final_size > (2 * 512 * 512):
            return _block_slicing(arrays, list_ndim, result_ndim)
        else:
>           return _block_concatenate(arrays, list_ndim, result_ndim)

/opt/miniconda3/envs/testbed/lib/python3.9/site-packages/numpy/core/shape_base.py:872: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

arrays = [[array([[1., 0., 0., 0., 0., 0.],
       [0., 1., 0., 0., 0., 0.],
       [0., 0., 1., 1., 0., 0.],
       [0., 0., 1...      [0., 0., 0., 0., 0., 0.],
       [0., 0., 0., 0., 0., 0.]]), array([[1., 1., 0., 0.],
       [0., 0., 1., 1.]])]]
list_ndim = 2, result_ndim = 2

    def _block_concatenate(arrays, list_ndim, result_ndim):
>       result = _block(arrays, list_ndim, result_ndim)

/opt/miniconda3/envs/testbed/lib/python3.9/site-packages/numpy/core/shape_base.py:916: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

arrays = [[array([[1., 0., 0., 0., 0., 0.],
       [0., 1., 0., 0., 0., 0.],
       [0., 0., 1., 1., 0., 0.],
       [0., 0., 1...      [0., 0., 0., 0., 0., 0.],
       [0., 0., 0., 0., 0., 0.]]), array([[1., 1., 0., 0.],
       [0., 0., 1., 1.]])]]
max_depth = 2, result_ndim = 2, depth = 0

    def _block(arrays, max_depth, result_ndim, depth=0):
        """
        Internal implementation of block based on repeated concatenation.
        `arrays` is the argument passed to
        block. `max_depth` is the depth of nested lists within `arrays` and
        `result_ndim` is the greatest of the dimensions of the arrays in
        `arrays` and the depth of the lists in `arrays` (see block docstring
        for details).
        """
        if depth < max_depth:
>           arrs = [_block(arr, max_depth, result_ndim, depth+1)
                    for arr in arrays]

/opt/miniconda3/envs/testbed/lib/python3.9/site-packages/numpy/core/shape_base.py:683: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

.0 = <list_iterator object at 0x7fbd1adf7bb0>

>   arrs = [_block(arr, max_depth, result_ndim, depth+1)
            for arr in arrays]

/opt/miniconda3/envs/testbed/lib/python3.9/site-packages/numpy/core/shape_base.py:683: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

arrays = [array([[1., 0., 0., 0., 0., 0.],
       [0., 1., 0., 0., 0., 0.],
       [0., 0., 1., 1., 0., 0.],
       [0., 0., 1....       [0., 0., 0., 0., 1., 0.],
       [0., 0., 0., 0., 0., 1.]]), array([[0., 0., 0., 0.],
       [0., 0., 0., 0.]])]
max_depth = 2, result_ndim = 2, depth = 1

    def _block(arrays, max_depth, result_ndim, depth=0):
        """
        Internal implementation of block based on repeated concatenation.
        `arrays` is the argument passed to
        block. `max_depth` is the depth of nested lists within `arrays` and
        `result_ndim` is the greatest of the dimensions of the arrays in
        `arrays` and the depth of the lists in `arrays` (see block docstring
        for details).
        """
        if depth < max_depth:
            arrs = [_block(arr, max_depth, result_ndim, depth+1)
                    for arr in arrays]
>           return _concatenate(arrs, axis=-(max_depth-depth))
E           ValueError: all the input array dimensions except for the concatenation axis must match exactly, but along dimension 0, the array at index 0 has size 8 and the array at index 1 has size 2

/opt/miniconda3/envs/testbed/lib/python3.9/site-packages/numpy/core/shape_base.py:685: ValueError
___________________ test_separable[compound_model2-result2] ____________________

compound_model = <CompoundModel(angle_1=2., factor_2=1.)>
result = (array([False, False,  True]), array([[ True, False],
       [ True, False],
       [False,  True]]))

    @pytest.mark.parametrize(('compound_model', 'result'), compound_models.values())
    def test_separable(compound_model, result):
>       assert_allclose(is_separable(compound_model), result[0])

astropy/modeling/tests/test_separable.py:134: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

args = (<function assert_allclose.<locals>.compare at 0x7fbd1ae4faf0>, array([ True,  True,  True, False, False,  True]), array([False, False,  True]))
kwds = {'equal_nan': True, 'err_msg': '', 'header': 'Not equal to tolerance rtol=1e-07, atol=0', 'verbose': True}

    @wraps(func)
    def inner(*args, **kwds):
        with self._recreate_cm():
>           return func(*args, **kwds)
E           AssertionError: 
E           Not equal to tolerance rtol=1e-07, atol=0
E           
E           (shapes (6,), (3,) mismatch)
E            x: array([ True,  True,  True, False, False,  True])
E            y: array([False, False,  True])

/opt/miniconda3/envs/testbed/lib/python3.9/contextlib.py:79: AssertionError
___________________ test_separable[compound_model3-result3] ____________________

compound_model = <CompoundModel(offset_0=1., offset_1=2., angle_3=2., factor_4=1.)>
result = (array([False, False,  True]), array([[ True, False],
       [ True, False],
       [False,  True]]))

    @pytest.mark.parametrize(('compound_model', 'result'), compound_models.values())
    def test_separable(compound_model, result):
>       assert_allclose(is_separable(compound_model), result[0])

astropy/modeling/tests/test_separable.py:134: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

transform = <CompoundModel(offset_0=1., offset_1=2., angle_3=2., factor_4=1.)>

    def is_separable(transform):
        """
        A separability test for the outputs of a transform.
    
        Parameters
        ----------
        transform : `~astropy.modeling.core.Model`
            A (compound) model.
    
        Returns
        -------
        is_separable : ndarray
            A boolean array with size ``transform.n_outputs`` where
            each element indicates whether the output is independent
            and the result of a separable transform.
    
        Examples
        --------
        >>> from astropy.modeling.models import Shift, Scale, Rotation2D, Polynomial2D
        >>> is_separable(Shift(1) & Shift(2) | Scale(1) & Scale(2))
            array([ True,  True]...)
        >>> is_separable(Shift(1) & Shift(2) | Rotation2D(2))
            array([False, False]...)
        >>> is_separable(Shift(1) & Shift(2) | Mapping([0, 1, 0, 1]) | \
            Polynomial2D(1) & Polynomial2D(2))
            array([False, False]...)
        >>> is_separable(Shift(1) & Shift(2) | Mapping([0, 1, 0, 1]))
            array([ True,  True,  True,  True]...)
    
        """
        if transform.n_inputs == 1 and transform.n_outputs > 1:
            is_separable = np.array([False] * transform.n_outputs).T
            return is_separable
>       separable_matrix = _separable(transform)

astropy/modeling/separable.py:60: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

transform = <CompoundModel(offset_0=1., offset_1=2., angle_3=2., factor_4=1.)>

    def _separable(transform):
        """
        Calculate the separability of outputs.
    
        Parameters
        ----------
        transform : `astropy.modeling.Model`
            A transform (usually a compound model).
    
        Returns :
        is_separable : ndarray of dtype np.bool
            An array of shape (transform.n_outputs,) of boolean type
            Each element represents the separablity of the corresponding output.
        """
        if (transform_matrix := transform._calculate_separability_matrix()) is not NotImplemented:
            return transform_matrix
        elif isinstance(transform, CompoundModel):
>           sepleft = _separable(transform.left)

astropy/modeling/separable.py:307: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

transform = <CompoundModel(offset_0=1., offset_1=2.)>

    def _separable(transform):
        """
        Calculate the separability of outputs.
    
        Parameters
        ----------
        transform : `astropy.modeling.Model`
            A transform (usually a compound model).
    
        Returns :
        is_separable : ndarray of dtype np.bool
            An array of shape (transform.n_outputs,) of boolean type
            Each element represents the separablity of the corresponding output.
        """
        if (transform_matrix := transform._calculate_separability_matrix()) is not NotImplemented:
            return transform_matrix
        elif isinstance(transform, CompoundModel):
            sepleft = _separable(transform.left)
            sepright = _separable(transform.right)
            if isinstance(transform.left, CompoundModel) or isinstance(transform.right, CompoundModel):
>               return np.block([[sepleft, np.zeros_like(sepright)],
                                 [np.zeros_like(sepleft), sepright]])

astropy/modeling/separable.py:310: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

arrays = [[array([[1., 0.],
       [0., 1.]]), array([[0., 0.],
       [0., 0.],
       [0., 0.]])], [array([[0., 0.],
       [0., 0.]]), array([[1., 0.],
       [1., 0.],
       [0., 1.]])]]

    @array_function_dispatch(_block_dispatcher)
    def block(arrays):
        """
        Assemble an nd-array from nested lists of blocks.
    
        Blocks in the innermost lists are concatenated (see `concatenate`) along
        the last dimension (-1), then these are concatenated along the
        second-last dimension (-2), and so on until the outermost list is reached.
    
        Blocks can be of any dimension, but will not be broadcasted using the normal
        rules. Instead, leading axes of size 1 are inserted, to make ``block.ndim``
        the same for all blocks. This is primarily useful for working with scalars,
        and means that code like ``np.block([v, 1])`` is valid, where
        ``v.ndim == 1``.
    
        When the nested list is two levels deep, this allows block matrices to be
        constructed from their components.
    
        .. versionadded:: 1.13.0
    
        Parameters
        ----------
        arrays : nested list of array_like or scalars (but not tuples)
            If passed a single ndarray or scalar (a nested list of depth 0), this
            is returned unmodified (and not copied).
    
            Elements shapes must match along the appropriate axes (without
            broadcasting), but leading 1s will be prepended to the shape as
            necessary to make the dimensions match.
    
        Returns
        -------
        block_array : ndarray
            The array assembled from the given blocks.
    
            The dimensionality of the output is equal to the greatest of:
            * the dimensionality of all the inputs
            * the depth to which the input list is nested
    
        Raises
        ------
        ValueError
            * If list depths are mismatched - for instance, ``[[a, b], c]`` is
              illegal, and should be spelt ``[[a, b], [c]]``
            * If lists are empty - for instance, ``[[a, b], []]``
    
        See Also
        --------
        concatenate : Join a sequence of arrays along an existing axis.
        stack : Join a sequence of arrays along a new axis.
        vstack : Stack arrays in sequence vertically (row wise).
        hstack : Stack arrays in sequence horizontally (column wise).
        dstack : Stack arrays in sequence depth wise (along third axis).
        column_stack : Stack 1-D arrays as columns into a 2-D array.
        vsplit : Split an array into multiple sub-arrays vertically (row-wise).
    
        Notes
        -----
    
        When called with only scalars, ``np.block`` is equivalent to an ndarray
        call. So ``np.block([[1, 2], [3, 4]])`` is equivalent to
        ``np.array([[1, 2], [3, 4]])``.
    
        This function does not enforce that the blocks lie on a fixed grid.
        ``np.block([[a, b], [c, d]])`` is not restricted to arrays of the form::
    
            AAAbb
            AAAbb
            cccDD
    
        But is also allowed to produce, for some ``a, b, c, d``::
    
            AAAbb
            AAAbb
            cDDDD
    
        Since concatenation happens along the last axis first, `block` is _not_
        capable of producing the following directly::
    
            AAAbb
            cccbb
            cccDD
    
        Matlab's "square bracket stacking", ``[A, B, ...; p, q, ...]``, is
        equivalent to ``np.block([[A, B, ...], [p, q, ...]])``.
    
        Examples
        --------
        The most common use of this function is to build a block matrix
    
        >>> A = np.eye(2) * 2
        >>> B = np.eye(3) * 3
        >>> np.block([
        ...     [A,               np.zeros((2, 3))],
        ...     [np.ones((3, 2)), B               ]
        ... ])
        array([[2., 0., 0., 0., 0.],
               [0., 2., 0., 0., 0.],
               [1., 1., 3., 0., 0.],
               [1., 1., 0., 3., 0.],
               [1., 1., 0., 0., 3.]])
    
        With a list of depth 1, `block` can be used as `hstack`
    
        >>> np.block([1, 2, 3])              # hstack([1, 2, 3])
        array([1, 2, 3])
    
        >>> a = np.array([1, 2, 3])
        >>> b = np.array([4, 5, 6])
        >>> np.block([a, b, 10])             # hstack([a, b, 10])
        array([ 1,  2,  3,  4,  5,  6, 10])
    
        >>> A = np.ones((2, 2), int)
        >>> B = 2 * A
        >>> np.block([A, B])                 # hstack([A, B])
        array([[1, 1, 2, 2],
               [1, 1, 2, 2]])
    
        With a list of depth 2, `block` can be used in place of `vstack`:
    
        >>> a = np.array([1, 2, 3])
        >>> b = np.array([4, 5, 6])
        >>> np.block([[a], [b]])             # vstack([a, b])
        array([[1, 2, 3],
               [4, 5, 6]])
    
        >>> A = np.ones((2, 2), int)
        >>> B = 2 * A
        >>> np.block([[A], [B]])             # vstack([A, B])
        array([[1, 1],
               [1, 1],
               [2, 2],
               [2, 2]])
    
        It can also be used in places of `atleast_1d` and `atleast_2d`
    
        >>> a = np.array(0)
        >>> b = np.array([1])
        >>> np.block([a])                    # atleast_1d(a)
        array([0])
        >>> np.block([b])                    # atleast_1d(b)
        array([1])
    
        >>> np.block([[a]])                  # atleast_2d(a)
        array([[0]])
        >>> np.block([[b]])                  # atleast_2d(b)
        array([[1]])
    
    
        """
        arrays, list_ndim, result_ndim, final_size = _block_setup(arrays)
    
        # It was found through benchmarking that making an array of final size
        # around 256x256 was faster by straight concatenation on a
        # i7-7700HQ processor and dual channel ram 2400MHz.
        # It didn't seem to matter heavily on the dtype used.
        #
        # A 2D array using repeated concatenation requires 2 copies of the array.
        #
        # The fastest algorithm will depend on the ratio of CPU power to memory
        # speed.
        # One can monitor the results of the benchmark
        # https://pv.github.io/numpy-bench/#bench_shape_base.Block2D.time_block2d
        # to tune this parameter until a C version of the `_block_info_recursion`
        # algorithm is implemented which would likely be faster than the python
        # version.
        if list_ndim * final_size > (2 * 512 * 512):
            return _block_slicing(arrays, list_ndim, result_ndim)
        else:
>           return _block_concatenate(arrays, list_ndim, result_ndim)

/opt/miniconda3/envs/testbed/lib/python3.9/site-packages/numpy/core/shape_base.py:872: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

arrays = [[array([[1., 0.],
       [0., 1.]]), array([[0., 0.],
       [0., 0.],
       [0., 0.]])], [array([[0., 0.],
       [0., 0.]]), array([[1., 0.],
       [1., 0.],
       [0., 1.]])]]
list_ndim = 2, result_ndim = 2

    def _block_concatenate(arrays, list_ndim, result_ndim):
>       result = _block(arrays, list_ndim, result_ndim)

/opt/miniconda3/envs/testbed/lib/python3.9/site-packages/numpy/core/shape_base.py:916: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

arrays = [[array([[1., 0.],
       [0., 1.]]), array([[0., 0.],
       [0., 0.],
       [0., 0.]])], [array([[0., 0.],
       [0., 0.]]), array([[1., 0.],
       [1., 0.],
       [0., 1.]])]]
max_depth = 2, result_ndim = 2, depth = 0

    def _block(arrays, max_depth, result_ndim, depth=0):
        """
        Internal implementation of block based on repeated concatenation.
        `arrays` is the argument passed to
        block. `max_depth` is the depth of nested lists within `arrays` and
        `result_ndim` is the greatest of the dimensions of the arrays in
        `arrays` and the depth of the lists in `arrays` (see block docstring
        for details).
        """
        if depth < max_depth:
>           arrs = [_block(arr, max_depth, result_ndim, depth+1)
                    for arr in arrays]

/opt/miniconda3/envs/testbed/lib/python3.9/site-packages/numpy/core/shape_base.py:683: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

.0 = <list_iterator object at 0x7fbd1adac520>

>   arrs = [_block(arr, max_depth, result_ndim, depth+1)
            for arr in arrays]

/opt/miniconda3/envs/testbed/lib/python3.9/site-packages/numpy/core/shape_base.py:683: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

arrays = [array([[1., 0.],
       [0., 1.]]), array([[0., 0.],
       [0., 0.],
       [0., 0.]])]
max_depth = 2, result_ndim = 2, depth = 1

    def _block(arrays, max_depth, result_ndim, depth=0):
        """
        Internal implementation of block based on repeated concatenation.
        `arrays` is the argument passed to
        block. `max_depth` is the depth of nested lists within `arrays` and
        `result_ndim` is the greatest of the dimensions of the arrays in
        `arrays` and the depth of the lists in `arrays` (see block docstring
        for details).
        """
        if depth < max_depth:
            arrs = [_block(arr, max_depth, result_ndim, depth+1)
                    for arr in arrays]
>           return _concatenate(arrs, axis=-(max_depth-depth))
E           ValueError: all the input array dimensions except for the concatenation axis must match exactly, but along dimension 0, the array at index 0 has size 2 and the array at index 1 has size 3

/opt/miniconda3/envs/testbed/lib/python3.9/site-packages/numpy/core/shape_base.py:685: ValueError
___________________ test_separable[compound_model5-result5] ____________________

compound_model = <CompoundModel(c0_0_1=0., c1_0_1=0., c0_1_1=0., offset_2=1.)>
result = (array([False,  True]), array([[ True, False],
       [False,  True]]))

    @pytest.mark.parametrize(('compound_model', 'result'), compound_models.values())
    def test_separable(compound_model, result):
>       assert_allclose(is_separable(compound_model), result[0])

astropy/modeling/tests/test_separable.py:134: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

transform = <CompoundModel(c0_0_1=0., c1_0_1=0., c0_1_1=0., offset_2=1.)>

    def is_separable(transform):
        """
        A separability test for the outputs of a transform.
    
        Parameters
        ----------
        transform : `~astropy.modeling.core.Model`
            A (compound) model.
    
        Returns
        -------
        is_separable : ndarray
            A boolean array with size ``transform.n_outputs`` where
            each element indicates whether the output is independent
            and the result of a separable transform.
    
        Examples
        --------
        >>> from astropy.modeling.models import Shift, Scale, Rotation2D, Polynomial2D
        >>> is_separable(Shift(1) & Shift(2) | Scale(1) & Scale(2))
            array([ True,  True]...)
        >>> is_separable(Shift(1) & Shift(2) | Rotation2D(2))
            array([False, False]...)
        >>> is_separable(Shift(1) & Shift(2) | Mapping([0, 1, 0, 1]) | \
            Polynomial2D(1) & Polynomial2D(2))
            array([False, False]...)
        >>> is_separable(Shift(1) & Shift(2) | Mapping([0, 1, 0, 1]))
            array([ True,  True,  True,  True]...)
    
        """
        if transform.n_inputs == 1 and transform.n_outputs > 1:
            is_separable = np.array([False] * transform.n_outputs).T
            return is_separable
>       separable_matrix = _separable(transform)

astropy/modeling/separable.py:60: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

transform = <CompoundModel(c0_0_1=0., c1_0_1=0., c0_1_1=0., offset_2=1.)>

    def _separable(transform):
        """
        Calculate the separability of outputs.
    
        Parameters
        ----------
        transform : `astropy.modeling.Model`
            A transform (usually a compound model).
    
        Returns :
        is_separable : ndarray of dtype np.bool
            An array of shape (transform.n_outputs,) of boolean type
            Each element represents the separablity of the corresponding output.
        """
        if (transform_matrix := transform._calculate_separability_matrix()) is not NotImplemented:
            return transform_matrix
        elif isinstance(transform, CompoundModel):
            sepleft = _separable(transform.left)
            sepright = _separable(transform.right)
            if isinstance(transform.left, CompoundModel) or isinstance(transform.right, CompoundModel):
>               return np.block([[sepleft, np.zeros_like(sepright)],
                                 [np.zeros_like(sepleft), sepright]])

astropy/modeling/separable.py:310: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

arrays = [[array([[1., 0.],
       [1., 0.],
       [0., 1.]]), array([[0., 0., 0.],
       [0., 0., 0.]])], [array([[0., 0.],
       [0., 0.],
       [0., 0.]]), array([[1., 1., 0.],
       [0., 0., 1.]])]]

    @array_function_dispatch(_block_dispatcher)
    def block(arrays):
        """
        Assemble an nd-array from nested lists of blocks.
    
        Blocks in the innermost lists are concatenated (see `concatenate`) along
        the last dimension (-1), then these are concatenated along the
        second-last dimension (-2), and so on until the outermost list is reached.
    
        Blocks can be of any dimension, but will not be broadcasted using the normal
        rules. Instead, leading axes of size 1 are inserted, to make ``block.ndim``
        the same for all blocks. This is primarily useful for working with scalars,
        and means that code like ``np.block([v, 1])`` is valid, where
        ``v.ndim == 1``.
    
        When the nested list is two levels deep, this allows block matrices to be
        constructed from their components.
    
        .. versionadded:: 1.13.0
    
        Parameters
        ----------
        arrays : nested list of array_like or scalars (but not tuples)
            If passed a single ndarray or scalar (a nested list of depth 0), this
            is returned unmodified (and not copied).
    
            Elements shapes must match along the appropriate axes (without
            broadcasting), but leading 1s will be prepended to the shape as
            necessary to make the dimensions match.
    
        Returns
        -------
        block_array : ndarray
            The array assembled from the given blocks.
    
            The dimensionality of the output is equal to the greatest of:
            * the dimensionality of all the inputs
            * the depth to which the input list is nested
    
        Raises
        ------
        ValueError
            * If list depths are mismatched - for instance, ``[[a, b], c]`` is
              illegal, and should be spelt ``[[a, b], [c]]``
            * If lists are empty - for instance, ``[[a, b], []]``
    
        See Also
        --------
        concatenate : Join a sequence of arrays along an existing axis.
        stack : Join a sequence of arrays along a new axis.
        vstack : Stack arrays in sequence vertically (row wise).
        hstack : Stack arrays in sequence horizontally (column wise).
        dstack : Stack arrays in sequence depth wise (along third axis).
        column_stack : Stack 1-D arrays as columns into a 2-D array.
        vsplit : Split an array into multiple sub-arrays vertically (row-wise).
    
        Notes
        -----
    
        When called with only scalars, ``np.block`` is equivalent to an ndarray
        call. So ``np.block([[1, 2], [3, 4]])`` is equivalent to
        ``np.array([[1, 2], [3, 4]])``.
    
        This function does not enforce that the blocks lie on a fixed grid.
        ``np.block([[a, b], [c, d]])`` is not restricted to arrays of the form::
    
            AAAbb
            AAAbb
            cccDD
    
        But is also allowed to produce, for some ``a, b, c, d``::
    
            AAAbb
            AAAbb
            cDDDD
    
        Since concatenation happens along the last axis first, `block` is _not_
        capable of producing the following directly::
    
            AAAbb
            cccbb
            cccDD
    
        Matlab's "square bracket stacking", ``[A, B, ...; p, q, ...]``, is
        equivalent to ``np.block([[A, B, ...], [p, q, ...]])``.
    
        Examples
        --------
        The most common use of this function is to build a block matrix
    
        >>> A = np.eye(2) * 2
        >>> B = np.eye(3) * 3
        >>> np.block([
        ...     [A,               np.zeros((2, 3))],
        ...     [np.ones((3, 2)), B               ]
        ... ])
        array([[2., 0., 0., 0., 0.],
               [0., 2., 0., 0., 0.],
               [1., 1., 3., 0., 0.],
               [1., 1., 0., 3., 0.],
               [1., 1., 0., 0., 3.]])
    
        With a list of depth 1, `block` can be used as `hstack`
    
        >>> np.block([1, 2, 3])              # hstack([1, 2, 3])
        array([1, 2, 3])
    
        >>> a = np.array([1, 2, 3])
        >>> b = np.array([4, 5, 6])
        >>> np.block([a, b, 10])             # hstack([a, b, 10])
        array([ 1,  2,  3,  4,  5,  6, 10])
    
        >>> A = np.ones((2, 2), int)
        >>> B = 2 * A
        >>> np.block([A, B])                 # hstack([A, B])
        array([[1, 1, 2, 2],
               [1, 1, 2, 2]])
    
        With a list of depth 2, `block` can be used in place of `vstack`:
    
        >>> a = np.array([1, 2, 3])
        >>> b = np.array([4, 5, 6])
        >>> np.block([[a], [b]])             # vstack([a, b])
        array([[1, 2, 3],
               [4, 5, 6]])
    
        >>> A = np.ones((2, 2), int)
        >>> B = 2 * A
        >>> np.block([[A], [B]])             # vstack([A, B])
        array([[1, 1],
               [1, 1],
               [2, 2],
               [2, 2]])
    
        It can also be used in places of `atleast_1d` and `atleast_2d`
    
        >>> a = np.array(0)
        >>> b = np.array([1])
        >>> np.block([a])                    # atleast_1d(a)
        array([0])
        >>> np.block([b])                    # atleast_1d(b)
        array([1])
    
        >>> np.block([[a]])                  # atleast_2d(a)
        array([[0]])
        >>> np.block([[b]])                  # atleast_2d(b)
        array([[1]])
    
    
        """
        arrays, list_ndim, result_ndim, final_size = _block_setup(arrays)
    
        # It was found through benchmarking that making an array of final size
        # around 256x256 was faster by straight concatenation on a
        # i7-7700HQ processor and dual channel ram 2400MHz.
        # It didn't seem to matter heavily on the dtype used.
        #
        # A 2D array using repeated concatenation requires 2 copies of the array.
        #
        # The fastest algorithm will depend on the ratio of CPU power to memory
        # speed.
        # One can monitor the results of the benchmark
        # https://pv.github.io/numpy-bench/#bench_shape_base.Block2D.time_block2d
        # to tune this parameter until a C version of the `_block_info_recursion`
        # algorithm is implemented which would likely be faster than the python
        # version.
        if list_ndim * final_size > (2 * 512 * 512):
            return _block_slicing(arrays, list_ndim, result_ndim)
        else:
>           return _block_concatenate(arrays, list_ndim, result_ndim)

/opt/miniconda3/envs/testbed/lib/python3.9/site-packages/numpy/core/shape_base.py:872: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

arrays = [[array([[1., 0.],
       [1., 0.],
       [0., 1.]]), array([[0., 0., 0.],
       [0., 0., 0.]])], [array([[0., 0.],
       [0., 0.],
       [0., 0.]]), array([[1., 1., 0.],
       [0., 0., 1.]])]]
list_ndim = 2, result_ndim = 2

    def _block_concatenate(arrays, list_ndim, result_ndim):
>       result = _block(arrays, list_ndim, result_ndim)

/opt/miniconda3/envs/testbed/lib/python3.9/site-packages/numpy/core/shape_base.py:916: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

arrays = [[array([[1., 0.],
       [1., 0.],
       [0., 1.]]), array([[0., 0., 0.],
       [0., 0., 0.]])], [array([[0., 0.],
       [0., 0.],
       [0., 0.]]), array([[1., 1., 0.],
       [0., 0., 1.]])]]
max_depth = 2, result_ndim = 2, depth = 0

    def _block(arrays, max_depth, result_ndim, depth=0):
        """
        Internal implementation of block based on repeated concatenation.
        `arrays` is the argument passed to
        block. `max_depth` is the depth of nested lists within `arrays` and
        `result_ndim` is the greatest of the dimensions of the arrays in
        `arrays` and the depth of the lists in `arrays` (see block docstring
        for details).
        """
        if depth < max_depth:
>           arrs = [_block(arr, max_depth, result_ndim, depth+1)
                    for arr in arrays]

/opt/miniconda3/envs/testbed/lib/python3.9/site-packages/numpy/core/shape_base.py:683: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

.0 = <list_iterator object at 0x7fbd1ade4df0>

>   arrs = [_block(arr, max_depth, result_ndim, depth+1)
            for arr in arrays]

/opt/miniconda3/envs/testbed/lib/python3.9/site-packages/numpy/core/shape_base.py:683: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

arrays = [array([[1., 0.],
       [1., 0.],
       [0., 1.]]), array([[0., 0., 0.],
       [0., 0., 0.]])]
max_depth = 2, result_ndim = 2, depth = 1

    def _block(arrays, max_depth, result_ndim, depth=0):
        """
        Internal implementation of block based on repeated concatenation.
        `arrays` is the argument passed to
        block. `max_depth` is the depth of nested lists within `arrays` and
        `result_ndim` is the greatest of the dimensions of the arrays in
        `arrays` and the depth of the lists in `arrays` (see block docstring
        for details).
        """
        if depth < max_depth:
            arrs = [_block(arr, max_depth, result_ndim, depth+1)
                    for arr in arrays]
>           return _concatenate(arrs, axis=-(max_depth-depth))
E           ValueError: all the input array dimensions except for the concatenation axis must match exactly, but along dimension 0, the array at index 0 has size 3 and the array at index 1 has size 2

/opt/miniconda3/envs/testbed/lib/python3.9/site-packages/numpy/core/shape_base.py:685: ValueError
==================================== PASSES ====================================
=========================== short test summary info ============================
PASSED astropy/modeling/tests/test_separable.py::test_coord_matrix
PASSED astropy/modeling/tests/test_separable.py::test_cdot
PASSED astropy/modeling/tests/test_separable.py::test_cstack
PASSED astropy/modeling/tests/test_separable.py::test_arith_oper
PASSED astropy/modeling/tests/test_separable.py::test_separable[compound_model4-result4]
PASSED astropy/modeling/tests/test_separable.py::test_custom_model_separable
FAILED astropy/modeling/tests/test_separable.py::test_separable[compound_model0-result0]
FAILED astropy/modeling/tests/test_separable.py::test_separable[compound_model1-result1]
FAILED astropy/modeling/tests/test_separable.py::test_separable[compound_model2-result2]
FAILED astropy/modeling/tests/test_separable.py::test_separable[compound_model3-result3]
FAILED astropy/modeling/tests/test_separable.py::test_separable[compound_model5-result5]
