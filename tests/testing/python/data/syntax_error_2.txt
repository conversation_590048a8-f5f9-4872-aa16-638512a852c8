Traceback (most recent call last):
  File "/opt/miniconda3/envs/testbed/bin/pytest", line 8, in <module>
    sys.exit(main())
  File "/testbed/src/_pytest/config/__init__.py", line 58, in main
    config = _prepareconfig(args, plugins)
  File "/testbed/src/_pytest/config/__init__.py", line 194, in _prepareconfig
    config = get_config(args, plugins)
  File "/testbed/src/_pytest/config/__init__.py", line 168, in get_config
    pluginmanager.import_plugin(spec)
  File "/testbed/src/_pytest/config/__init__.py", line 552, in import_plugin
    __import__(importspec)
  File "/testbed/src/_pytest/junitxml.py", line 26, in <module>
    class Junit(py.xml.Namespace):
  File "/testbed/src/_pytest/junitxml.py", line 27, in Junit
    testsuite = py.xml.Namespace.testsuite(
  File "/opt/miniconda3/envs/testbed/lib/python3.9/site-packages/py/_xmlgen.py", line 27, in __getattr__
    raise ValueError("Namespace class is abstract")
ValueError: Namespace class is abstract
