============================= test session starts ==============================
collected 85 items

tests/_core/test_scales.py ............................................. [ 52%]
.........x..............................                                 [100%]

================================== XFAILURES ===================================
_________________ TestNominal.test_color_numeric_int_float_mix _________________

self = <tests._core.test_scales.TestNominal object at 0x7fcfd020c460>

    @pytest.mark.xfail(reason="Need to sort out float/int order")
    def test_color_numeric_int_float_mix(self):

        z = pd.Series([1, 2], name="z")
        s = Nominal(order=[1.0, 2])._setup(z, Color())
        c1, c2 = color_palette(n_colors=2)
        null = (np.nan, np.nan, np.nan)
>       assert_array_equal(s(z), [c1, null, c2])

tests/_core/test_scales.py:440:
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
seaborn/_core/scales.py:124: in __call__
    trans_data = func(trans_data)
seaborn/_core/properties.py:646: in mapping
    out[use] = np.take(colors, ixs[use], axis=0)
/opt/miniconda3/envs/testbed/lib/python3.9/site-packages/numpy/core/fromnumeric.py:192: in take
    return _wrapfunc(a, 'take', indices, axis=axis, out=out, mode=mode)
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _

obj = array([[0.12156863, 0.46666667, 0.70588235],
       [1.        , 0.49803922, 0.05490196]])
method = 'take', args = (array([2, 3]),)
kwds = {'axis': 0, 'mode': 'raise', 'out': None}
bound = <built-in method take of numpy.ndarray object at 0x7fcfcfedb570>

    def _wrapfunc(obj, method, *args, **kwds):
        bound = getattr(obj, method, None)
        if bound is None:
            return _wrapit(obj, method, *args, **kwds)

        try:
>           return bound(*args, **kwds)
E           IndexError: index 2 is out of bounds for axis 0 with size 2

/opt/miniconda3/envs/testbed/lib/python3.9/site-packages/numpy/core/fromnumeric.py:59: IndexError
=============================== warnings summary ===============================
tests/_core/test_scales.py::TestNominal::test_color_numeric_with_order_subset
  /testbed/seaborn/_core/properties.py:643: RuntimeWarning: invalid value encountered in cast
    ixs = np.asarray(x, np.intp)

tests/_core/test_scales.py::TestNominal::test_object_order_subset
  /testbed/seaborn/_core/properties.py:366: RuntimeWarning: invalid value encountered in cast
    ixs = np.asarray(x, np.intp)

-- Docs: https://docs.pytest.org/en/stable/how-to/capture-warnings.html
==================================== PASSES ====================================
=========================== short test summary info ============================
PASSED tests/_core/test_scales.py::TestContinuous::test_coordinate_defaults
PASSED tests/_core/test_scales.py::TestContinuous::test_coordinate_transform
PASSED tests/_core/test_scales.py::TestContinuous::test_coordinate_transform_with_parameter
PASSED tests/_core/test_scales.py::TestContinuous::test_coordinate_transform_error
PASSED tests/_core/test_scales.py::TestContinuous::test_interval_defaults
PASSED tests/_core/test_scales.py::TestContinuous::test_interval_with_range
PASSED tests/_core/test_scales.py::TestContinuous::test_interval_with_norm
PASSED tests/_core/test_scales.py::TestContinuous::test_interval_with_range_norm_and_transform
PASSED tests/_core/test_scales.py::TestContinuous::test_color_defaults
PASSED tests/_core/test_scales.py::TestContinuous::test_color_named_values
PASSED tests/_core/test_scales.py::TestContinuous::test_color_tuple_values
PASSED tests/_core/test_scales.py::TestContinuous::test_color_callable_values
PASSED tests/_core/test_scales.py::TestContinuous::test_color_with_norm
PASSED tests/_core/test_scales.py::TestContinuous::test_color_with_transform
PASSED tests/_core/test_scales.py::TestContinuous::test_tick_locator
PASSED tests/_core/test_scales.py::TestContinuous::test_tick_locator_input_check
PASSED tests/_core/test_scales.py::TestContinuous::test_tick_upto
PASSED tests/_core/test_scales.py::TestContinuous::test_tick_every
PASSED tests/_core/test_scales.py::TestContinuous::test_tick_every_between
PASSED tests/_core/test_scales.py::TestContinuous::test_tick_at
PASSED tests/_core/test_scales.py::TestContinuous::test_tick_count
PASSED tests/_core/test_scales.py::TestContinuous::test_tick_count_between
PASSED tests/_core/test_scales.py::TestContinuous::test_tick_minor
PASSED tests/_core/test_scales.py::TestContinuous::test_log_tick_default
PASSED tests/_core/test_scales.py::TestContinuous::test_log_tick_upto
PASSED tests/_core/test_scales.py::TestContinuous::test_log_tick_count
PASSED tests/_core/test_scales.py::TestContinuous::test_log_tick_format_disabled
PASSED tests/_core/test_scales.py::TestContinuous::test_log_tick_every
PASSED tests/_core/test_scales.py::TestContinuous::test_symlog_tick_default
PASSED tests/_core/test_scales.py::TestContinuous::test_label_formatter
PASSED tests/_core/test_scales.py::TestContinuous::test_label_like_pattern
PASSED tests/_core/test_scales.py::TestContinuous::test_label_like_string
PASSED tests/_core/test_scales.py::TestContinuous::test_label_like_function
PASSED tests/_core/test_scales.py::TestContinuous::test_label_base
PASSED tests/_core/test_scales.py::TestContinuous::test_label_unit
PASSED tests/_core/test_scales.py::TestContinuous::test_label_unit_with_sep
PASSED tests/_core/test_scales.py::TestContinuous::test_label_empty_unit
PASSED tests/_core/test_scales.py::TestContinuous::test_label_base_from_transform
PASSED tests/_core/test_scales.py::TestContinuous::test_label_type_checks
PASSED tests/_core/test_scales.py::TestNominal::test_coordinate_defaults
PASSED tests/_core/test_scales.py::TestNominal::test_coordinate_with_order
PASSED tests/_core/test_scales.py::TestNominal::test_coordinate_with_subset_order
PASSED tests/_core/test_scales.py::TestNominal::test_coordinate_axis
PASSED tests/_core/test_scales.py::TestNominal::test_coordinate_axis_with_order
PASSED tests/_core/test_scales.py::TestNominal::test_coordinate_axis_with_subset_order
PASSED tests/_core/test_scales.py::TestNominal::test_coordinate_axis_with_category_dtype
PASSED tests/_core/test_scales.py::TestNominal::test_coordinate_numeric_data
PASSED tests/_core/test_scales.py::TestNominal::test_coordinate_numeric_data_with_order
PASSED tests/_core/test_scales.py::TestNominal::test_color_defaults
PASSED tests/_core/test_scales.py::TestNominal::test_color_named_palette
PASSED tests/_core/test_scales.py::TestNominal::test_color_list_palette
PASSED tests/_core/test_scales.py::TestNominal::test_color_dict_palette
PASSED tests/_core/test_scales.py::TestNominal::test_color_numeric_data
PASSED tests/_core/test_scales.py::TestNominal::test_color_numeric_with_order_subset
PASSED tests/_core/test_scales.py::TestNominal::test_color_alpha_in_palette
PASSED tests/_core/test_scales.py::TestNominal::test_color_unknown_palette
PASSED tests/_core/test_scales.py::TestNominal::test_object_defaults
PASSED tests/_core/test_scales.py::TestNominal::test_object_list
PASSED tests/_core/test_scales.py::TestNominal::test_object_dict
PASSED tests/_core/test_scales.py::TestNominal::test_object_order
PASSED tests/_core/test_scales.py::TestNominal::test_object_order_subset
PASSED tests/_core/test_scales.py::TestNominal::test_objects_that_are_weird
PASSED tests/_core/test_scales.py::TestNominal::test_alpha_default
PASSED tests/_core/test_scales.py::TestNominal::test_fill
PASSED tests/_core/test_scales.py::TestNominal::test_fill_dict
PASSED tests/_core/test_scales.py::TestNominal::test_fill_nunique_warning
PASSED tests/_core/test_scales.py::TestNominal::test_interval_defaults
PASSED tests/_core/test_scales.py::TestNominal::test_interval_tuple
PASSED tests/_core/test_scales.py::TestNominal::test_interval_tuple_numeric
PASSED tests/_core/test_scales.py::TestNominal::test_interval_list
PASSED tests/_core/test_scales.py::TestNominal::test_interval_dict
PASSED tests/_core/test_scales.py::TestNominal::test_interval_with_transform
PASSED tests/_core/test_scales.py::TestNominal::test_empty_data
PASSED tests/_core/test_scales.py::TestTemporal::test_coordinate_defaults
PASSED tests/_core/test_scales.py::TestTemporal::test_interval_defaults
PASSED tests/_core/test_scales.py::TestTemporal::test_interval_with_range
PASSED tests/_core/test_scales.py::TestTemporal::test_interval_with_norm
PASSED tests/_core/test_scales.py::TestTemporal::test_color_defaults
PASSED tests/_core/test_scales.py::TestTemporal::test_color_named_values
PASSED tests/_core/test_scales.py::TestTemporal::test_coordinate_axis
PASSED tests/_core/test_scales.py::TestTemporal::test_tick_locator
PASSED tests/_core/test_scales.py::TestTemporal::test_tick_upto
PASSED tests/_core/test_scales.py::TestTemporal::test_label_formatter
PASSED tests/_core/test_scales.py::TestTemporal::test_label_concise
XFAIL tests/_core/test_scales.py::TestNominal::test_color_numeric_int_float_mix - Need to sort out float/int order
================== 84 passed, 1 xfailed, 2 warnings in 3.89s ===================
