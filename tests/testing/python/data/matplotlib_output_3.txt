============================= test session starts ==============================
collected 11 items

tests/_core/test_groupby.py ...........                                  [100%]

==================================== PASSES ====================================
=========================== short test summary info ============================
PASSED tests/_core/test_groupby.py::test_init_from_list
PASSED tests/_core/test_groupby.py::test_init_from_dict
PASSED tests/_core/test_groupby.py::test_init_requires_order
PASSED tests/_core/test_groupby.py::test_at_least_one_grouping_variable_required
PASSED tests/_core/test_groupby.py::test_agg_one_grouper
PASSED tests/_core/test_groupby.py::test_agg_two_groupers
PASSED tests/_core/test_groupby.py::test_agg_two_groupers_ordered
PASSED tests/_core/test_groupby.py::test_apply_no_grouper
PASSED tests/_core/test_groupby.py::test_apply_one_grouper
PASSED tests/_core/test_groupby.py::test_apply_mutate_columns
PASSED tests/_core/test_groupby.py::test_apply_replace_columns
============================== 11 passed in 0.59s ==============================