>>>>> Run tests
============================= test session starts ==============================
platform linux -- Python 3.9.19, pytest-7.4.4, pluggy-1.5.0
benchmark: 4.0.0 (defaults: timer=time.perf_counter disable_gc=False min_rounds=5 min_time=0.000005 max_time=1.0 calibration_precision=10 warmup=False warmup_iterations=100000)
rootdir: /testbed
configfile: setup.cfg
plugins: timeout-2.3.1, cov-3.0.0, forked-1.6.0, benchmark-4.0.0, profiling-1.7.0, xdist-2.5.0
collected 62 items

tests/checkers/unittest_imports.py ....FF                                [  9%]
tests/lint/unittest_lint.py ..................................F......... [ 80%]
............                                                             [100%]

=================================== FAILURES ===================================
_________________ TestImportsChecker.test_wildcard_import_init _________________

self = <checkers.unittest_imports.TestImportsChecker object at 0x7f2e967ad310>

    def test_wildcard_import_init(self) -> None:
        module = astroid.MANAGER.ast_from_module_name("init_wildcard", REGR_DATA)
        import_from = module.body[0]
    
        with self.assertNoMessages():
>           self.checker.visit_importfrom(import_from)

tests/checkers/unittest_imports.py:82: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 
/opt/miniconda3/envs/testbed/lib/python3.9/contextlib.py:126: in __exit__
    next(self.gen)
pylint/testutils/checker_test_case.py:40: in assertNoMessages
    yield
/opt/miniconda3/envs/testbed/lib/python3.9/contextlib.py:126: in __exit__
    next(self.gen)
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

self = <checkers.unittest_imports.TestImportsChecker object at 0x7f2e967ad310>
ignore_position = False, messages = ()
got = [MessageTest(msg_id='import-error', line=1, node=<ImportFrom l.1 at 0x7f2e96718250>, args="'empty'", confidence=Confid...EFINED', description='Warning without any associated confidence level.'), col_offset=0, end_line=1, end_col_offset=19)]
no_msg = 'No message.'

    @contextlib.contextmanager
    def assertAddsMessages(
        self, *messages: MessageTest, ignore_position: bool = False
    ) -> Generator[None, None, None]:
        """Assert that exactly the given method adds the given messages.
    
        The list of messages must exactly match *all* the messages added by the
        method. Additionally, we check to see whether the args in each message can
        actually be substituted into the message string.
    
        Using the keyword argument `ignore_position`, all checks for position
        arguments (line, col_offset, ...) will be skipped. This can be used to
        just test messages for the correct node.
        """
        yield
        got = self.linter.release_messages()
        no_msg = "No message."
        expected = "\n".join(repr(m) for m in messages) or no_msg
        got_str = "\n".join(repr(m) for m in got) or no_msg
        msg = (
            "Expected messages did not match actual.\n"
            f"\nExpected:\n{expected}\n\nGot:\n{got_str}\n"
        )
    
>       assert len(messages) == len(got), msg
E       AssertionError: Expected messages did not match actual.
E         
E         Expected:
E         No message.
E         
E         Got:
E         MessageTest(msg_id='import-error', line=1, node=<ImportFrom l.1 at 0x7f2e96718250>, args="'empty'", confidence=Confidence(name='UNDEFINED', description='Warning without any associated confidence level.'), col_offset=0, end_line=1, end_col_offset=19)
E         
E       assert 0 == 1
E        +  where 0 = len(())
E        +  and   1 = len([MessageTest(msg_id='import-error', line=1, node=<ImportFrom l.1 at 0x7f2e96718250>, args="'empty'", confidence=Confid...EFINED', description='Warning without any associated confidence level.'), col_offset=0, end_line=1, end_col_offset=19)])

pylint/testutils/checker_test_case.py:66: AssertionError
_______________ TestImportsChecker.test_wildcard_import_non_init _______________

self = <checkers.unittest_imports.TestImportsChecker object at 0x7f2e967ad100>

    def test_wildcard_import_non_init(self) -> None:
        module = astroid.MANAGER.ast_from_module_name("wildcard", REGR_DATA)
        import_from = module.body[0]
    
        msg = MessageTest(
            msg_id="wildcard-import",
            node=import_from,
            args="empty",
            confidence=UNDEFINED,
            line=1,
            col_offset=0,
            end_line=1,
            end_col_offset=19,
        )
        with self.assertAddsMessages(msg):
>           self.checker.visit_importfrom(import_from)

tests/checkers/unittest_imports.py:99: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 
/opt/miniconda3/envs/testbed/lib/python3.9/contextlib.py:126: in __exit__
    next(self.gen)
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

self = <checkers.unittest_imports.TestImportsChecker object at 0x7f2e967ad100>
ignore_position = False
messages = (MessageTest(msg_id='wildcard-import', line=1, node=<ImportFrom l.1 at 0x7f2e96674340>, args='empty', confidence=Confi...FINED', description='Warning without any associated confidence level.'), col_offset=0, end_line=1, end_col_offset=19),)
got = [MessageTest(msg_id='import-error', line=1, node=<ImportFrom l.1 at 0x7f2e96674340>, args="'empty'", confidence=Confid...EFINED', description='Warning without any associated confidence level.'), col_offset=0, end_line=1, end_col_offset=19)]
no_msg = 'No message.'

    @contextlib.contextmanager
    def assertAddsMessages(
        self, *messages: MessageTest, ignore_position: bool = False
    ) -> Generator[None, None, None]:
        """Assert that exactly the given method adds the given messages.
    
        The list of messages must exactly match *all* the messages added by the
        method. Additionally, we check to see whether the args in each message can
        actually be substituted into the message string.
    
        Using the keyword argument `ignore_position`, all checks for position
        arguments (line, col_offset, ...) will be skipped. This can be used to
        just test messages for the correct node.
        """
        yield
        got = self.linter.release_messages()
        no_msg = "No message."
        expected = "\n".join(repr(m) for m in messages) or no_msg
        got_str = "\n".join(repr(m) for m in got) or no_msg
        msg = (
            "Expected messages did not match actual.\n"
            f"\nExpected:\n{expected}\n\nGot:\n{got_str}\n"
        )
    
>       assert len(messages) == len(got), msg
E       AssertionError: Expected messages did not match actual.
E         
E         Expected:
E         MessageTest(msg_id='wildcard-import', line=1, node=<ImportFrom l.1 at 0x7f2e96674340>, args='empty', confidence=Confidence(name='UNDEFINED', description='Warning without any associated confidence level.'), col_offset=0, end_line=1, end_col_offset=19)
E         
E         Got:
E         MessageTest(msg_id='import-error', line=1, node=<ImportFrom l.1 at 0x7f2e96674340>, args="'empty'", confidence=Confidence(name='UNDEFINED', description='Warning without any associated confidence level.'), col_offset=0, end_line=1, end_col_offset=19)
E         MessageTest(msg_id='wildcard-import', line=1, node=<ImportFrom l.1 at 0x7f2e96674340>, args='empty', confidence=Confidence(name='UNDEFINED', description='Warning without any associated confidence level.'), col_offset=0, end_line=1, end_col_offset=19)
E         
E       assert 1 == 2
E        +  where 1 = len((MessageTest(msg_id='wildcard-import', line=1, node=<ImportFrom l.1 at 0x7f2e96674340>, args='empty', confidence=Confi...FINED', description='Warning without any associated confidence level.'), col_offset=0, end_line=1, end_col_offset=19),))
E        +  and   2 = len([MessageTest(msg_id='import-error', line=1, node=<ImportFrom l.1 at 0x7f2e96674340>, args="'empty'", confidence=Confid...EFINED', description='Warning without any associated confidence level.'), col_offset=0, end_line=1, end_col_offset=19)])

pylint/testutils/checker_test_case.py:66: AssertionError
_________________________ test_analyze_explicit_script _________________________

linter = Checker 'main' (responsible for 'F0001', 'F0002', 'F0010', 'F0011', 'I0001', 'I0010', 'I0011', 'I0013', 'I0020', 'I0021', 'I0022', 'E0001', 'E0011', 'W0012', 'R0022', 'E0013', 'E0014', 'E0015')

    def test_analyze_explicit_script(linter: PyLinter) -> None:
        linter.set_reporter(testutils.GenericTestReporter())
        linter.check([os.path.join(DATA_DIR, "ascript")])
>       assert len(linter.reporter.messages) == 1
E       AssertionError: assert 0 == 1
E        +  where 0 = len([])
E        +    where [] = <pylint.testutils.reporter_for_tests.GenericTestReporter object at 0x7f2e9652ef40>.messages
E        +      where <pylint.testutils.reporter_for_tests.GenericTestReporter object at 0x7f2e9652ef40> = Checker 'main' (responsible for 'F0001', 'F0002', 'F0010', 'F0011', 'I0001', 'I0010', 'I0011', 'I0013', 'I0020', 'I0021', 'I0022', 'E0001', 'E0011', 'W0012', 'R0022', 'E0013', 'E0014', 'E0015').reporter

tests/lint/unittest_lint.py:575: AssertionError
==================================== PASSES ====================================
__________________ test_init_hooks_called_before_load_plugins __________________
----------------------------- Captured stderr call -----------------------------
Option --init-hook expects a value
=========================== short test summary info ============================
PASSED tests/checkers/unittest_imports.py::TestImportsChecker::test_relative_beyond_top_level
PASSED tests/checkers/unittest_imports.py::TestImportsChecker::test_relative_beyond_top_level_two
PASSED tests/checkers/unittest_imports.py::TestImportsChecker::test_relative_beyond_top_level_three
PASSED tests/checkers/unittest_imports.py::TestImportsChecker::test_relative_beyond_top_level_four
PASSED tests/lint/unittest_lint.py::test_no_args
PASSED tests/lint/unittest_lint.py::test_one_arg[case0]
PASSED tests/lint/unittest_lint.py::test_one_arg[case1]
PASSED tests/lint/unittest_lint.py::test_one_arg[case2]
PASSED tests/lint/unittest_lint.py::test_one_arg[case3]
PASSED tests/lint/unittest_lint.py::test_one_arg[case4]
PASSED tests/lint/unittest_lint.py::test_two_similar_args[case0]
PASSED tests/lint/unittest_lint.py::test_two_similar_args[case1]
PASSED tests/lint/unittest_lint.py::test_two_similar_args[case2]
PASSED tests/lint/unittest_lint.py::test_two_similar_args[case3]
PASSED tests/lint/unittest_lint.py::test_more_args[case0]
PASSED tests/lint/unittest_lint.py::test_more_args[case1]
PASSED tests/lint/unittest_lint.py::test_more_args[case2]
PASSED tests/lint/unittest_lint.py::test_pylint_visit_method_taken_in_account
PASSED tests/lint/unittest_lint.py::test_enable_message
PASSED tests/lint/unittest_lint.py::test_enable_message_category
PASSED tests/lint/unittest_lint.py::test_message_state_scope
PASSED tests/lint/unittest_lint.py::test_enable_message_block
PASSED tests/lint/unittest_lint.py::test_enable_by_symbol
PASSED tests/lint/unittest_lint.py::test_enable_report
PASSED tests/lint/unittest_lint.py::test_report_output_format_aliased
PASSED tests/lint/unittest_lint.py::test_set_unsupported_reporter
PASSED tests/lint/unittest_lint.py::test_set_option_1
PASSED tests/lint/unittest_lint.py::test_set_option_2
PASSED tests/lint/unittest_lint.py::test_enable_checkers
PASSED tests/lint/unittest_lint.py::test_errors_only
PASSED tests/lint/unittest_lint.py::test_disable_similar
PASSED tests/lint/unittest_lint.py::test_disable_alot
PASSED tests/lint/unittest_lint.py::test_addmessage
PASSED tests/lint/unittest_lint.py::test_addmessage_invalid
PASSED tests/lint/unittest_lint.py::test_load_plugin_command_line
PASSED tests/lint/unittest_lint.py::test_load_plugin_config_file
PASSED tests/lint/unittest_lint.py::test_load_plugin_configuration
PASSED tests/lint/unittest_lint.py::test_init_hooks_called_before_load_plugins
PASSED tests/lint/unittest_lint.py::test_full_documentation
PASSED tests/lint/unittest_lint.py::test_list_msgs_enabled
PASSED tests/lint/unittest_lint.py::test_pylint_home
PASSED tests/lint/unittest_lint.py::test_pylint_home_from_environ
PASSED tests/lint/unittest_lint.py::test_warn_about_old_home
PASSED tests/lint/unittest_lint.py::test_pylintrc
PASSED tests/lint/unittest_lint.py::test_pylintrc_parentdir
PASSED tests/lint/unittest_lint.py::test_pylintrc_parentdir_no_package
PASSED tests/lint/unittest_lint.py::test_custom_should_analyze_file
PASSED tests/lint/unittest_lint.py::test_multiprocessing[1]
PASSED tests/lint/unittest_lint.py::test_multiprocessing[2]
PASSED tests/lint/unittest_lint.py::test_filename_with__init__
PASSED tests/lint/unittest_lint.py::test_by_module_statement_value
PASSED tests/lint/unittest_lint.py::test_recursive_ignore[--ignore-failing.py]
PASSED tests/lint/unittest_lint.py::test_recursive_ignore[--ignore-ignored_subdirectory]
PASSED tests/lint/unittest_lint.py::test_recursive_ignore[--ignore-patterns-failing.*]
PASSED tests/lint/unittest_lint.py::test_recursive_ignore[--ignore-patterns-ignored_*]
PASSED tests/lint/unittest_lint.py::test_recursive_ignore[--ignore-paths-.*directory/ignored.*]
PASSED tests/lint/unittest_lint.py::test_recursive_ignore[--ignore-paths-.*ignored.*/failing.*]
PASSED tests/lint/unittest_lint.py::test_import_sibling_module_from_namespace
PASSED tests/lint/unittest_lint.py::test_lint_namespace_package_under_dir
FAILED tests/checkers/unittest_imports.py::TestImportsChecker::test_wildcard_import_init
FAILED tests/checkers/unittest_imports.py::TestImportsChecker::test_wildcard_import_non_init
FAILED tests/lint/unittest_lint.py::test_analyze_explicit_script - AssertionE...
========================= 3 failed, 59 passed in 8.37s =========================

