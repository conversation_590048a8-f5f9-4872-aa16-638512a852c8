============================= test session starts ==============================
collected 86 items

tests/_core/test_scales.py FFFFFFFFFFFFFFF.FFFFFFFFFFF.FFFFFFFFFF.F..... [ 52%]
..........x...................FFFFFFFFFFF                                [100%]

=================================== FAILURES ===================================
___________________ TestContinuous.test_coordinate_defaults ____________________

self = <tests._core.test_scales.TestContinuous object at 0x7fe9f1a04670>
x = 0    1.0
1    3.0
2    9.0
Name: x, dtype: float64

    def test_coordinate_defaults(self, x):

>       s = Continuous()._setup(x, Coordinate())

tests/_core/test_scales.py:52:
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
seaborn/_core/scales.py:332: in _setup
    forward, inverse = new._get_transform()
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _

self = Continuous(values=None, norm=None, trans=None)

    def _get_transform(self):

>       if isinstance(self._data, Series) and self._data.dtype == bool:
E       AttributeError: 'Continuous' object has no attribute '_data'

seaborn/_core/scales.py:400: AttributeError
___________________ TestContinuous.test_coordinate_transform ___________________

self = <tests._core.test_scales.TestContinuous object at 0x7fe9f1a047f0>
x = 0    1.0
1    3.0
2    9.0
Name: x, dtype: float64

    def test_coordinate_transform(self, x):

>       s = Continuous(trans="log")._setup(x, Coordinate())

tests/_core/test_scales.py:57:
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
seaborn/_core/scales.py:332: in _setup
    forward, inverse = new._get_transform()
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _

self = Continuous(values=None, norm=None, trans='log')

    def _get_transform(self):

>       if isinstance(self._data, Series) and self._data.dtype == bool:
E       AttributeError: 'Continuous' object has no attribute '_data'

seaborn/_core/scales.py:400: AttributeError
___________ TestContinuous.test_coordinate_transform_with_parameter ____________

self = <tests._core.test_scales.TestContinuous object at 0x7fe9f1a049d0>
x = 0    1.0
1    3.0
2    9.0
Name: x, dtype: float64

    def test_coordinate_transform_with_parameter(self, x):

>       s = Continuous(trans="pow3")._setup(x, Coordinate())

tests/_core/test_scales.py:62:
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
seaborn/_core/scales.py:332: in _setup
    forward, inverse = new._get_transform()
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _

self = Continuous(values=None, norm=None, trans='pow3')

    def _get_transform(self):

>       if isinstance(self._data, Series) and self._data.dtype == bool:
E       AttributeError: 'Continuous' object has no attribute '_data'

seaborn/_core/scales.py:400: AttributeError
________________ TestContinuous.test_coordinate_transform_error ________________

self = <tests._core.test_scales.TestContinuous object at 0x7fe9f1a04bb0>
x = 0    1.0
1    3.0
2    9.0
Name: x, dtype: float64

    def test_coordinate_transform_error(self, x):

        s = Continuous(trans="bad")
        with pytest.raises(ValueError, match="Unknown value provided"):
>           s._setup(x, Coordinate())

tests/_core/test_scales.py:69:
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
seaborn/_core/scales.py:332: in _setup
    forward, inverse = new._get_transform()
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _

self = Continuous(values=None, norm=None, trans='bad')

    def _get_transform(self):

>       if isinstance(self._data, Series) and self._data.dtype == bool:
E       AttributeError: 'Continuous' object has no attribute '_data'

seaborn/_core/scales.py:400: AttributeError
____________________ TestContinuous.test_interval_defaults _____________________

self = <tests._core.test_scales.TestContinuous object at 0x7fe9f1a04d90>
x = 0    1.0
1    3.0
2    9.0
Name: x, dtype: float64

    def test_interval_defaults(self, x):

>       s = Continuous()._setup(x, IntervalProperty())

tests/_core/test_scales.py:73:
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
seaborn/_core/scales.py:332: in _setup
    forward, inverse = new._get_transform()
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _

self = Continuous(values=None, norm=None, trans=None)

    def _get_transform(self):

>       if isinstance(self._data, Series) and self._data.dtype == bool:
E       AttributeError: 'Continuous' object has no attribute '_data'

seaborn/_core/scales.py:400: AttributeError
___________________ TestContinuous.test_interval_with_range ____________________

self = <tests._core.test_scales.TestContinuous object at 0x7fe9f1a04f70>
x = 0    1.0
1    3.0
2    9.0
Name: x, dtype: float64

    def test_interval_with_range(self, x):

>       s = Continuous((1, 3))._setup(x, IntervalProperty())

tests/_core/test_scales.py:78:
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
seaborn/_core/scales.py:332: in _setup
    forward, inverse = new._get_transform()
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _

self = Continuous(values=(1, 3), norm=None, trans=None)

    def _get_transform(self):

>       if isinstance(self._data, Series) and self._data.dtype == bool:
E       AttributeError: 'Continuous' object has no attribute '_data'

seaborn/_core/scales.py:400: AttributeError
____________________ TestContinuous.test_interval_with_norm ____________________

self = <tests._core.test_scales.TestContinuous object at 0x7fe9f1a048e0>
x = 0    1.0
1    3.0
2    9.0
Name: x, dtype: float64

    def test_interval_with_norm(self, x):

>       s = Continuous(norm=(3, 7))._setup(x, IntervalProperty())

tests/_core/test_scales.py:83:
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
seaborn/_core/scales.py:332: in _setup
    forward, inverse = new._get_transform()
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _

self = Continuous(values=None, norm=(3, 7), trans=None)

    def _get_transform(self):

>       if isinstance(self._data, Series) and self._data.dtype == bool:
E       AttributeError: 'Continuous' object has no attribute '_data'

seaborn/_core/scales.py:400: AttributeError
__________ TestContinuous.test_interval_with_range_norm_and_transform __________

self = <tests._core.test_scales.TestContinuous object at 0x7fe9f1a041f0>
x = 0      1
1     10
2    100
dtype: int64

    def test_interval_with_range_norm_and_transform(self, x):

        x = pd.Series([1, 10, 100])
        # TODO param order?
>       s = Continuous((2, 3), (10, 100), "log")._setup(x, IntervalProperty())

tests/_core/test_scales.py:90:
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
seaborn/_core/scales.py:332: in _setup
    forward, inverse = new._get_transform()
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _

self = Continuous(values=(2, 3), norm=(10, 100), trans='log')

    def _get_transform(self):

>       if isinstance(self._data, Series) and self._data.dtype == bool:
E       AttributeError: 'Continuous' object has no attribute '_data'

seaborn/_core/scales.py:400: AttributeError
______________________ TestContinuous.test_color_defaults ______________________

self = <tests._core.test_scales.TestContinuous object at 0x7fe9f1a18190>
x = 0    1.0
1    3.0
2    9.0
Name: x, dtype: float64

    def test_color_defaults(self, x):

        cmap = color_palette("ch:", as_cmap=True)
>       s = Continuous()._setup(x, Color())

tests/_core/test_scales.py:96:
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
seaborn/_core/scales.py:332: in _setup
    forward, inverse = new._get_transform()
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _

self = Continuous(values=None, norm=None, trans=None)

    def _get_transform(self):

>       if isinstance(self._data, Series) and self._data.dtype == bool:
E       AttributeError: 'Continuous' object has no attribute '_data'

seaborn/_core/scales.py:400: AttributeError
____________________ TestContinuous.test_color_named_values ____________________

self = <tests._core.test_scales.TestContinuous object at 0x7fe9f1a183a0>
x = 0    1.0
1    3.0
2    9.0
Name: x, dtype: float64

    def test_color_named_values(self, x):

        cmap = color_palette("viridis", as_cmap=True)
>       s = Continuous("viridis")._setup(x, Color())

tests/_core/test_scales.py:102:
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
seaborn/_core/scales.py:332: in _setup
    forward, inverse = new._get_transform()
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _

self = Continuous(values='viridis', norm=None, trans=None)

    def _get_transform(self):

>       if isinstance(self._data, Series) and self._data.dtype == bool:
E       AttributeError: 'Continuous' object has no attribute '_data'

seaborn/_core/scales.py:400: AttributeError
____________________ TestContinuous.test_color_tuple_values ____________________

self = <tests._core.test_scales.TestContinuous object at 0x7fe9f1a185b0>
x = 0    1.0
1    3.0
2    9.0
Name: x, dtype: float64

    def test_color_tuple_values(self, x):

        cmap = color_palette("blend:b,g", as_cmap=True)
>       s = Continuous(("b", "g"))._setup(x, Color())

tests/_core/test_scales.py:108:
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
seaborn/_core/scales.py:332: in _setup
    forward, inverse = new._get_transform()
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _

self = Continuous(values=('b', 'g'), norm=None, trans=None)

    def _get_transform(self):

>       if isinstance(self._data, Series) and self._data.dtype == bool:
E       AttributeError: 'Continuous' object has no attribute '_data'

seaborn/_core/scales.py:400: AttributeError
__________________ TestContinuous.test_color_callable_values ___________________

self = <tests._core.test_scales.TestContinuous object at 0x7fe9f1a187c0>
x = 0    1.0
1    3.0
2    9.0
Name: x, dtype: float64

    def test_color_callable_values(self, x):

        cmap = color_palette("light:r", as_cmap=True)
>       s = Continuous(cmap)._setup(x, Color())

tests/_core/test_scales.py:114:
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
seaborn/_core/scales.py:332: in _setup
    forward, inverse = new._get_transform()
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _

self = Continuous(values=<matplotlib.colors.LinearSegmentedColormap object at 0x7fe9f1b63790>, norm=None, trans=None)

    def _get_transform(self):

>       if isinstance(self._data, Series) and self._data.dtype == bool:
E       AttributeError: 'Continuous' object has no attribute '_data'

seaborn/_core/scales.py:400: AttributeError
_____________________ TestContinuous.test_color_with_norm ______________________

self = <tests._core.test_scales.TestContinuous object at 0x7fe9f1a189d0>
x = 0    1.0
1    3.0
2    9.0
Name: x, dtype: float64

    def test_color_with_norm(self, x):

        cmap = color_palette("ch:", as_cmap=True)
>       s = Continuous(norm=(3, 7))._setup(x, Color())

tests/_core/test_scales.py:120:
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
seaborn/_core/scales.py:332: in _setup
    forward, inverse = new._get_transform()
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _

self = Continuous(values=None, norm=(3, 7), trans=None)

    def _get_transform(self):

>       if isinstance(self._data, Series) and self._data.dtype == bool:
E       AttributeError: 'Continuous' object has no attribute '_data'

seaborn/_core/scales.py:400: AttributeError
___________________ TestContinuous.test_color_with_transform ___________________

self = <tests._core.test_scales.TestContinuous object at 0x7fe9f1a18be0>
x = 0      1.0
1     10.0
2    100.0
Name: x, dtype: float64

    def test_color_with_transform(self, x):

        x = pd.Series([1, 10, 100], name="x", dtype=float)
        cmap = color_palette("ch:", as_cmap=True)
>       s = Continuous(trans="log")._setup(x, Color())

tests/_core/test_scales.py:127:
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
seaborn/_core/scales.py:332: in _setup
    forward, inverse = new._get_transform()
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _

self = Continuous(values=None, norm=None, trans='log')

    def _get_transform(self):

>       if isinstance(self._data, Series) and self._data.dtype == bool:
E       AttributeError: 'Continuous' object has no attribute '_data'

seaborn/_core/scales.py:400: AttributeError
_______________________ TestContinuous.test_tick_locator _______________________

self = <tests._core.test_scales.TestContinuous object at 0x7fe9f1a18df0>
x = 0    1.0
1    3.0
2    9.0
Name: x, dtype: float64

    def test_tick_locator(self, x):

        locs = [.2, .5, .8]
        locator = mpl.ticker.FixedLocator(locs)
>       a = self.setup_ticks(x, locator)

tests/_core/test_scales.py:134:
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
tests/_core/test_scales.py:37: in setup_ticks
    s = Continuous().tick(*args, **kwargs)._setup(x, Coordinate())
seaborn/_core/scales.py:332: in _setup
    forward, inverse = new._get_transform()
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _

self = Continuous(values=None, norm=None, trans=None)

    def _get_transform(self):

>       if isinstance(self._data, Series) and self._data.dtype == bool:
E       AttributeError: 'Continuous' object has no attribute '_data'

seaborn/_core/scales.py:400: AttributeError
________________________ TestContinuous.test_tick_upto _________________________

self = <tests._core.test_scales.TestContinuous object at 0x7fe9f1a11250>
x = 0    1.0
1    3.0
2    9.0
Name: x, dtype: float64

    def test_tick_upto(self, x):

        for n in [2, 5, 10]:
>           a = self.setup_ticks(x, upto=n)

tests/_core/test_scales.py:146:
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
tests/_core/test_scales.py:37: in setup_ticks
    s = Continuous().tick(*args, **kwargs)._setup(x, Coordinate())
seaborn/_core/scales.py:332: in _setup
    forward, inverse = new._get_transform()
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _

self = Continuous(values=None, norm=None, trans=None)

    def _get_transform(self):

>       if isinstance(self._data, Series) and self._data.dtype == bool:
E       AttributeError: 'Continuous' object has no attribute '_data'

seaborn/_core/scales.py:400: AttributeError
________________________ TestContinuous.test_tick_every ________________________

self = <tests._core.test_scales.TestContinuous object at 0x7fe9f1a11460>
x = 0    1.0
1    3.0
2    9.0
Name: x, dtype: float64

    def test_tick_every(self, x):

        for d in [.05, .2, .5]:
>           a = self.setup_ticks(x, every=d)

tests/_core/test_scales.py:152:
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
tests/_core/test_scales.py:37: in setup_ticks
    s = Continuous().tick(*args, **kwargs)._setup(x, Coordinate())
seaborn/_core/scales.py:332: in _setup
    forward, inverse = new._get_transform()
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _

self = Continuous(values=None, norm=None, trans=None)

    def _get_transform(self):

>       if isinstance(self._data, Series) and self._data.dtype == bool:
E       AttributeError: 'Continuous' object has no attribute '_data'

seaborn/_core/scales.py:400: AttributeError
____________________ TestContinuous.test_tick_every_between ____________________

self = <tests._core.test_scales.TestContinuous object at 0x7fe9f1a11670>
x = 0    1.0
1    3.0
2    9.0
Name: x, dtype: float64

    def test_tick_every_between(self, x):

        lo, hi = .2, .8
        for d in [.05, .2, .5]:
>           a = self.setup_ticks(x, every=d, between=(lo, hi))

tests/_core/test_scales.py:159:
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
tests/_core/test_scales.py:37: in setup_ticks
    s = Continuous().tick(*args, **kwargs)._setup(x, Coordinate())
seaborn/_core/scales.py:332: in _setup
    forward, inverse = new._get_transform()
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _

self = Continuous(values=None, norm=None, trans=None)

    def _get_transform(self):

>       if isinstance(self._data, Series) and self._data.dtype == bool:
E       AttributeError: 'Continuous' object has no attribute '_data'

seaborn/_core/scales.py:400: AttributeError
_________________________ TestContinuous.test_tick_at __________________________

self = <tests._core.test_scales.TestContinuous object at 0x7fe9f1a11880>
x = 0    1.0
1    3.0
2    9.0
Name: x, dtype: float64

    def test_tick_at(self, x):

        locs = [.2, .5, .9]
>       a = self.setup_ticks(x, at=locs)

tests/_core/test_scales.py:166:
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
tests/_core/test_scales.py:37: in setup_ticks
    s = Continuous().tick(*args, **kwargs)._setup(x, Coordinate())
seaborn/_core/scales.py:332: in _setup
    forward, inverse = new._get_transform()
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _

self = Continuous(values=None, norm=None, trans=None)

    def _get_transform(self):

>       if isinstance(self._data, Series) and self._data.dtype == bool:
E       AttributeError: 'Continuous' object has no attribute '_data'

seaborn/_core/scales.py:400: AttributeError
________________________ TestContinuous.test_tick_count ________________________

self = <tests._core.test_scales.TestContinuous object at 0x7fe9f1a11a90>
x = 0    1.0
1    3.0
2    9.0
Name: x, dtype: float64

    def test_tick_count(self, x):

        n = 8
>       a = self.setup_ticks(x, count=n)

tests/_core/test_scales.py:172:
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
tests/_core/test_scales.py:37: in setup_ticks
    s = Continuous().tick(*args, **kwargs)._setup(x, Coordinate())
seaborn/_core/scales.py:332: in _setup
    forward, inverse = new._get_transform()
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _

self = Continuous(values=None, norm=None, trans=None)

    def _get_transform(self):

>       if isinstance(self._data, Series) and self._data.dtype == bool:
E       AttributeError: 'Continuous' object has no attribute '_data'

seaborn/_core/scales.py:400: AttributeError
____________________ TestContinuous.test_tick_count_between ____________________

self = <tests._core.test_scales.TestContinuous object at 0x7fe9f1a11ca0>
x = 0    1.0
1    3.0
2    9.0
Name: x, dtype: float64

    def test_tick_count_between(self, x):

        n = 5
        lo, hi = .2, .7
>       a = self.setup_ticks(x, count=n, between=(lo, hi))

tests/_core/test_scales.py:179:
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
tests/_core/test_scales.py:37: in setup_ticks
    s = Continuous().tick(*args, **kwargs)._setup(x, Coordinate())
seaborn/_core/scales.py:332: in _setup
    forward, inverse = new._get_transform()
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _

self = Continuous(values=None, norm=None, trans=None)

    def _get_transform(self):

>       if isinstance(self._data, Series) and self._data.dtype == bool:
E       AttributeError: 'Continuous' object has no attribute '_data'

seaborn/_core/scales.py:400: AttributeError
________________________ TestContinuous.test_tick_minor ________________________

self = <tests._core.test_scales.TestContinuous object at 0x7fe9f1a11eb0>
x = 0    1.0
1    3.0
2    9.0
Name: x, dtype: float64

    def test_tick_minor(self, x):

        n = 3
>       a = self.setup_ticks(x, count=2, minor=n)

tests/_core/test_scales.py:185:
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
tests/_core/test_scales.py:37: in setup_ticks
    s = Continuous().tick(*args, **kwargs)._setup(x, Coordinate())
seaborn/_core/scales.py:332: in _setup
    forward, inverse = new._get_transform()
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _

self = Continuous(values=None, norm=None, trans=None)

    def _get_transform(self):

>       if isinstance(self._data, Series) and self._data.dtype == bool:
E       AttributeError: 'Continuous' object has no attribute '_data'

seaborn/_core/scales.py:400: AttributeError
_____________________ TestContinuous.test_log_tick_default _____________________

self = <tests._core.test_scales.TestContinuous object at 0x7fe9f1a11be0>
x = 0    1.0
1    3.0
2    9.0
Name: x, dtype: float64

    def test_log_tick_default(self, x):

>       s = Continuous(trans="log")._setup(x, Coordinate())

tests/_core/test_scales.py:193:
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
seaborn/_core/scales.py:332: in _setup
    forward, inverse = new._get_transform()
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _

self = Continuous(values=None, norm=None, trans='log')

    def _get_transform(self):

>       if isinstance(self._data, Series) and self._data.dtype == bool:
E       AttributeError: 'Continuous' object has no attribute '_data'

seaborn/_core/scales.py:400: AttributeError
______________________ TestContinuous.test_log_tick_upto _______________________

self = <tests._core.test_scales.TestContinuous object at 0x7fe9f1a11340>
x = 0    1.0
1    3.0
2    9.0
Name: x, dtype: float64

    def test_log_tick_upto(self, x):

        n = 3
>       s = Continuous(trans="log").tick(upto=n)._setup(x, Coordinate())

tests/_core/test_scales.py:202:
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
seaborn/_core/scales.py:332: in _setup
    forward, inverse = new._get_transform()
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _

self = Continuous(values=None, norm=None, trans='log')

    def _get_transform(self):

>       if isinstance(self._data, Series) and self._data.dtype == bool:
E       AttributeError: 'Continuous' object has no attribute '_data'

seaborn/_core/scales.py:400: AttributeError
______________________ TestContinuous.test_log_tick_count ______________________

self = <tests._core.test_scales.TestContinuous object at 0x7fe9f1a18a00>
x = 0    1.0
1    3.0
2    9.0
Name: x, dtype: float64

    def test_log_tick_count(self, x):

        with pytest.raises(RuntimeError, match="`count` requires"):
            Continuous(trans="log").tick(count=4)

        s = Continuous(trans="log").tick(count=4, between=(1, 1000))
>       a = PseudoAxis(s._setup(x, Coordinate())._matplotlib_scale)

tests/_core/test_scales.py:212:
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
seaborn/_core/scales.py:332: in _setup
    forward, inverse = new._get_transform()
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _

self = Continuous(values=None, norm=None, trans='log')

    def _get_transform(self):

>       if isinstance(self._data, Series) and self._data.dtype == bool:
E       AttributeError: 'Continuous' object has no attribute '_data'

seaborn/_core/scales.py:400: AttributeError
_________________ TestContinuous.test_log_tick_format_disabled _________________

self = <tests._core.test_scales.TestContinuous object at 0x7fe9f1a182e0>
x = 0    1.0
1    3.0
2    9.0
Name: x, dtype: float64

    def test_log_tick_format_disabled(self, x):

>       s = Continuous(trans="log").label(base=None)._setup(x, Coordinate())

tests/_core/test_scales.py:218:
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
seaborn/_core/scales.py:332: in _setup
    forward, inverse = new._get_transform()
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _

self = Continuous(values=None, norm=None, trans='log')

    def _get_transform(self):

>       if isinstance(self._data, Series) and self._data.dtype == bool:
E       AttributeError: 'Continuous' object has no attribute '_data'

seaborn/_core/scales.py:400: AttributeError
___________________ TestContinuous.test_symlog_tick_default ____________________

self = <tests._core.test_scales.TestContinuous object at 0x7fe9f1a281c0>
x = 0    1.0
1    3.0
2    9.0
Name: x, dtype: float64

    def test_symlog_tick_default(self, x):

>       s = Continuous(trans="symlog")._setup(x, Coordinate())

tests/_core/test_scales.py:232:
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
seaborn/_core/scales.py:332: in _setup
    forward, inverse = new._get_transform()
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _

self = Continuous(values=None, norm=None, trans='symlog')

    def _get_transform(self):

>       if isinstance(self._data, Series) and self._data.dtype == bool:
E       AttributeError: 'Continuous' object has no attribute '_data'

seaborn/_core/scales.py:400: AttributeError
_____________________ TestContinuous.test_label_formatter ______________________

self = <tests._core.test_scales.TestContinuous object at 0x7fe9f1a283d0>
x = 0    1.0
1    3.0
2    9.0
Name: x, dtype: float64

    def test_label_formatter(self, x):

        fmt = mpl.ticker.FormatStrFormatter("%.3f")
>       a, locs = self.setup_labels(x, fmt)

tests/_core/test_scales.py:244:
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
tests/_core/test_scales.py:44: in setup_labels
    s = Continuous().label(*args, **kwargs)._setup(x, Coordinate())
seaborn/_core/scales.py:332: in _setup
    forward, inverse = new._get_transform()
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _

self = Continuous(values=None, norm=None, trans=None)

    def _get_transform(self):

>       if isinstance(self._data, Series) and self._data.dtype == bool:
E       AttributeError: 'Continuous' object has no attribute '_data'

seaborn/_core/scales.py:400: AttributeError
____________________ TestContinuous.test_label_like_pattern ____________________

self = <tests._core.test_scales.TestContinuous object at 0x7fe9f1a285e0>
x = 0    1.0
1    3.0
2    9.0
Name: x, dtype: float64

    def test_label_like_pattern(self, x):

>       a, locs = self.setup_labels(x, like=".4f")

tests/_core/test_scales.py:251:
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
tests/_core/test_scales.py:44: in setup_labels
    s = Continuous().label(*args, **kwargs)._setup(x, Coordinate())
seaborn/_core/scales.py:332: in _setup
    forward, inverse = new._get_transform()
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _

self = Continuous(values=None, norm=None, trans=None)

    def _get_transform(self):

>       if isinstance(self._data, Series) and self._data.dtype == bool:
E       AttributeError: 'Continuous' object has no attribute '_data'

seaborn/_core/scales.py:400: AttributeError
____________________ TestContinuous.test_label_like_string _____________________

self = <tests._core.test_scales.TestContinuous object at 0x7fe9f1a287f0>
x = 0    1.0
1    3.0
2    9.0
Name: x, dtype: float64

    def test_label_like_string(self, x):

>       a, locs = self.setup_labels(x, like="x = {x:.1f}")

tests/_core/test_scales.py:258:
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
tests/_core/test_scales.py:44: in setup_labels
    s = Continuous().label(*args, **kwargs)._setup(x, Coordinate())
seaborn/_core/scales.py:332: in _setup
    forward, inverse = new._get_transform()
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _

self = Continuous(values=None, norm=None, trans=None)

    def _get_transform(self):

>       if isinstance(self._data, Series) and self._data.dtype == bool:
E       AttributeError: 'Continuous' object has no attribute '_data'

seaborn/_core/scales.py:400: AttributeError
___________________ TestContinuous.test_label_like_function ____________________

self = <tests._core.test_scales.TestContinuous object at 0x7fe9f1a28a00>
x = 0    1.0
1    3.0
2    9.0
Name: x, dtype: float64

    def test_label_like_function(self, x):

>       a, locs = self.setup_labels(x, like="{:^5.1f}".format)

tests/_core/test_scales.py:265:
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
tests/_core/test_scales.py:44: in setup_labels
    s = Continuous().label(*args, **kwargs)._setup(x, Coordinate())
seaborn/_core/scales.py:332: in _setup
    forward, inverse = new._get_transform()
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _

self = Continuous(values=None, norm=None, trans=None)

    def _get_transform(self):

>       if isinstance(self._data, Series) and self._data.dtype == bool:
E       AttributeError: 'Continuous' object has no attribute '_data'

seaborn/_core/scales.py:400: AttributeError
________________________ TestContinuous.test_label_base ________________________

self = <tests._core.test_scales.TestContinuous object at 0x7fe9f1a28c10>
x = 0    1.0
1    3.0
2    9.0
Name: x, dtype: float64

    def test_label_base(self, x):

>       a, locs = self.setup_labels(100 * x, base=2)

tests/_core/test_scales.py:272:
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
tests/_core/test_scales.py:44: in setup_labels
    s = Continuous().label(*args, **kwargs)._setup(x, Coordinate())
seaborn/_core/scales.py:332: in _setup
    forward, inverse = new._get_transform()
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _

self = Continuous(values=None, norm=None, trans=None)

    def _get_transform(self):

>       if isinstance(self._data, Series) and self._data.dtype == bool:
E       AttributeError: 'Continuous' object has no attribute '_data'

seaborn/_core/scales.py:400: AttributeError
________________________ TestContinuous.test_label_unit ________________________

self = <tests._core.test_scales.TestContinuous object at 0x7fe9f1a28e20>
x = 0    1.0
1    3.0
2    9.0
Name: x, dtype: float64

    def test_label_unit(self, x):

>       a, locs = self.setup_labels(1000 * x, unit="g")

tests/_core/test_scales.py:279:
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
tests/_core/test_scales.py:44: in setup_labels
    s = Continuous().label(*args, **kwargs)._setup(x, Coordinate())
seaborn/_core/scales.py:332: in _setup
    forward, inverse = new._get_transform()
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _

self = Continuous(values=None, norm=None, trans=None)

    def _get_transform(self):

>       if isinstance(self._data, Series) and self._data.dtype == bool:
E       AttributeError: 'Continuous' object has no attribute '_data'

seaborn/_core/scales.py:400: AttributeError
___________________ TestContinuous.test_label_unit_with_sep ____________________

self = <tests._core.test_scales.TestContinuous object at 0x7fe9f1a2d070>
x = 0    1.0
1    3.0
2    9.0
Name: x, dtype: float64

    def test_label_unit_with_sep(self, x):

>       a, locs = self.setup_labels(1000 * x, unit=("", "g"))

tests/_core/test_scales.py:286:
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
tests/_core/test_scales.py:44: in setup_labels
    s = Continuous().label(*args, **kwargs)._setup(x, Coordinate())
seaborn/_core/scales.py:332: in _setup
    forward, inverse = new._get_transform()
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _

self = Continuous(values=None, norm=None, trans=None)

    def _get_transform(self):

>       if isinstance(self._data, Series) and self._data.dtype == bool:
E       AttributeError: 'Continuous' object has no attribute '_data'

seaborn/_core/scales.py:400: AttributeError
_____________________ TestContinuous.test_label_empty_unit _____________________

self = <tests._core.test_scales.TestContinuous object at 0x7fe9f1a2d280>
x = 0    1.0
1    3.0
2    9.0
Name: x, dtype: float64

    def test_label_empty_unit(self, x):

>       a, locs = self.setup_labels(1000 * x, unit="")

tests/_core/test_scales.py:293:
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
tests/_core/test_scales.py:44: in setup_labels
    s = Continuous().label(*args, **kwargs)._setup(x, Coordinate())
seaborn/_core/scales.py:332: in _setup
    forward, inverse = new._get_transform()
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _

self = Continuous(values=None, norm=None, trans=None)

    def _get_transform(self):

>       if isinstance(self._data, Series) and self._data.dtype == bool:
E       AttributeError: 'Continuous' object has no attribute '_data'

seaborn/_core/scales.py:400: AttributeError
________________ TestContinuous.test_label_base_from_transform _________________

self = <tests._core.test_scales.TestContinuous object at 0x7fe9f1a2d490>
x = 0    1.0
1    3.0
2    9.0
Name: x, dtype: float64

    def test_label_base_from_transform(self, x):

        s = Continuous(trans="log")
>       a = PseudoAxis(s._setup(x, Coordinate())._matplotlib_scale)

tests/_core/test_scales.py:301:
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
seaborn/_core/scales.py:332: in _setup
    forward, inverse = new._get_transform()
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _

self = Continuous(values=None, norm=None, trans='log')

    def _get_transform(self):

>       if isinstance(self._data, Series) and self._data.dtype == bool:
E       AttributeError: 'Continuous' object has no attribute '_data'

seaborn/_core/scales.py:400: AttributeError
_________________ TestContinuous.test_color_with_boolean_data __________________

self = <tests._core.test_scales.TestContinuous object at 0x7fe9f1a2d8b0>

    def test_color_with_boolean_data(self):

        bool_data = pd.Series([True, False, True], name="bool_data")
>       s = Continuous()._setup(bool_data, Color())

tests/_core/test_scales.py:318:
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
seaborn/_core/scales.py:332: in _setup
    forward, inverse = new._get_transform()
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _

self = Continuous(values=None, norm=None, trans=None)

    def _get_transform(self):

>       if isinstance(self._data, Series) and self._data.dtype == bool:
E       AttributeError: 'Continuous' object has no attribute '_data'

seaborn/_core/scales.py:400: AttributeError
____________________ TestTemporal.test_coordinate_defaults _____________________

self = <tests._core.test_scales.TestTemporal object at 0x7fe9f19c4dc0>
t = 0   1972-09-27
1   1975-06-24
2   1980-12-14
Name: x, dtype: datetime64[ns]
x = 0    1000.0
1    2000.0
2    4000.0
Name: x, dtype: float64

    def test_coordinate_defaults(self, t, x):

>       s = Temporal()._setup(t, Coordinate())

tests/_core/test_scales.py:588:
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
seaborn/_core/scales.py:332: in _setup
    forward, inverse = new._get_transform()
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _

self = Temporal(values=None, norm=None)

    def _get_transform(self):

>       if isinstance(self._data, Series) and self._data.dtype == bool:
E       AttributeError: 'Temporal' object has no attribute '_data'

seaborn/_core/scales.py:400: AttributeError
_____________________ TestTemporal.test_interval_defaults ______________________

self = <tests._core.test_scales.TestTemporal object at 0x7fe9f19cb040>
t = 0   1972-09-27
1   1975-06-24
2   1980-12-14
Name: x, dtype: datetime64[ns]
x = 0    1000.0
1    2000.0
2    4000.0
Name: x, dtype: float64

    def test_interval_defaults(self, t, x):

>       s = Temporal()._setup(t, IntervalProperty())

tests/_core/test_scales.py:593:
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
seaborn/_core/scales.py:332: in _setup
    forward, inverse = new._get_transform()
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _

self = Temporal(values=None, norm=None)

    def _get_transform(self):

>       if isinstance(self._data, Series) and self._data.dtype == bool:
E       AttributeError: 'Temporal' object has no attribute '_data'

seaborn/_core/scales.py:400: AttributeError
____________________ TestTemporal.test_interval_with_range _____________________

self = <tests._core.test_scales.TestTemporal object at 0x7fe9f19cb220>
t = 0   1972-09-27
1   1975-06-24
2   1980-12-14
Name: x, dtype: datetime64[ns]
x = 0    1000.0
1    2000.0
2    4000.0
Name: x, dtype: float64

    def test_interval_with_range(self, t, x):

        values = (1, 3)
>       s = Temporal((1, 3))._setup(t, IntervalProperty())

tests/_core/test_scales.py:600:
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
seaborn/_core/scales.py:332: in _setup
    forward, inverse = new._get_transform()
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _

self = Temporal(values=(1, 3), norm=None)

    def _get_transform(self):

>       if isinstance(self._data, Series) and self._data.dtype == bool:
E       AttributeError: 'Temporal' object has no attribute '_data'

seaborn/_core/scales.py:400: AttributeError
_____________________ TestTemporal.test_interval_with_norm _____________________

self = <tests._core.test_scales.TestTemporal object at 0x7fe9f19cb430>
t = 0   1972-09-27
1   1975-06-24
2   1980-12-14
Name: x, dtype: datetime64[ns]
x = 0    1000.0
1    2000.0
2    4000.0
Name: x, dtype: float64

    def test_interval_with_norm(self, t, x):

        norm = t[1], t[2]
>       s = Temporal(norm=norm)._setup(t, IntervalProperty())

tests/_core/test_scales.py:608:
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
seaborn/_core/scales.py:332: in _setup
    forward, inverse = new._get_transform()
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _

self = Temporal(values=None, norm=(Timestamp('1975-06-24 00:00:00'), Timestamp('1980-12-14 00:00:00')))

    def _get_transform(self):

>       if isinstance(self._data, Series) and self._data.dtype == bool:
E       AttributeError: 'Temporal' object has no attribute '_data'

seaborn/_core/scales.py:400: AttributeError
_______________________ TestTemporal.test_color_defaults _______________________

self = <tests._core.test_scales.TestTemporal object at 0x7fe9f19cb640>
t = 0   1972-09-27
1   1975-06-24
2   1980-12-14
Name: x, dtype: datetime64[ns]
x = 0    1000.0
1    2000.0
2    4000.0
Name: x, dtype: float64

    def test_color_defaults(self, t, x):

        cmap = color_palette("ch:", as_cmap=True)
>       s = Temporal()._setup(t, Color())

tests/_core/test_scales.py:616:
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
seaborn/_core/scales.py:332: in _setup
    forward, inverse = new._get_transform()
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _

self = Temporal(values=None, norm=None)

    def _get_transform(self):

>       if isinstance(self._data, Series) and self._data.dtype == bool:
E       AttributeError: 'Temporal' object has no attribute '_data'

seaborn/_core/scales.py:400: AttributeError
_____________________ TestTemporal.test_color_named_values _____________________

self = <tests._core.test_scales.TestTemporal object at 0x7fe9f19cb850>
t = 0   1972-09-27
1   1975-06-24
2   1980-12-14
Name: x, dtype: datetime64[ns]
x = 0    1000.0
1    2000.0
2    4000.0
Name: x, dtype: float64

    def test_color_named_values(self, t, x):

        name = "viridis"
        cmap = color_palette(name, as_cmap=True)
>       s = Temporal(name)._setup(t, Color())

tests/_core/test_scales.py:624:
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
seaborn/_core/scales.py:332: in _setup
    forward, inverse = new._get_transform()
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _

self = Temporal(values='viridis', norm=None)

    def _get_transform(self):

>       if isinstance(self._data, Series) and self._data.dtype == bool:
E       AttributeError: 'Temporal' object has no attribute '_data'

seaborn/_core/scales.py:400: AttributeError
______________________ TestTemporal.test_coordinate_axis _______________________

self = <tests._core.test_scales.TestTemporal object at 0x7fe9f19cba60>
t = 0   1972-09-27
1   1975-06-24
2   1980-12-14
Name: x, dtype: datetime64[ns]
x = 0    1000.0
1    2000.0
2    4000.0
Name: x, dtype: float64

    def test_coordinate_axis(self, t, x):

        ax = mpl.figure.Figure().subplots()
>       s = Temporal()._setup(t, Coordinate(), ax.xaxis)

tests/_core/test_scales.py:631:
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
seaborn/_core/scales.py:332: in _setup
    forward, inverse = new._get_transform()
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _

self = Temporal(values=None, norm=None)

    def _get_transform(self):

>       if isinstance(self._data, Series) and self._data.dtype == bool:
E       AttributeError: 'Temporal' object has no attribute '_data'

seaborn/_core/scales.py:400: AttributeError
________________________ TestTemporal.test_tick_locator ________________________

self = <tests._core.test_scales.TestTemporal object at 0x7fe9f19c4ca0>
t = 0   1972-09-27
1   1975-06-24
2   1980-12-14
Name: x, dtype: datetime64[ns]

    @pytest.mark.skipif(
        Version(mpl.__version__) < Version("3.3.0"),
        reason="Test requires new matplotlib date epoch."
    )
    def test_tick_locator(self, t):

        locator = mpl.dates.YearLocator(month=3, day=15)
        s = Temporal().tick(locator)
>       a = PseudoAxis(s._setup(t, Coordinate())._matplotlib_scale)

tests/_core/test_scales.py:646:
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
seaborn/_core/scales.py:332: in _setup
    forward, inverse = new._get_transform()
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _

self = Temporal(values=None, norm=None)

    def _get_transform(self):

>       if isinstance(self._data, Series) and self._data.dtype == bool:
E       AttributeError: 'Temporal' object has no attribute '_data'

seaborn/_core/scales.py:400: AttributeError
_________________________ TestTemporal.test_tick_upto __________________________

self = <tests._core.test_scales.TestTemporal object at 0x7fe9f19c4280>
t = 0   1972-09-27
1   1975-06-24
2   1980-12-14
Name: x, dtype: datetime64[ns]
x = 0    1000.0
1    2000.0
2    4000.0
Name: x, dtype: float64

    def test_tick_upto(self, t, x):

        n = 8
        ax = mpl.figure.Figure().subplots()
>       Temporal().tick(upto=n)._setup(t, Coordinate(), ax.xaxis)

tests/_core/test_scales.py:654:
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
seaborn/_core/scales.py:332: in _setup
    forward, inverse = new._get_transform()
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _

self = Temporal(values=None, norm=None)

    def _get_transform(self):

>       if isinstance(self._data, Series) and self._data.dtype == bool:
E       AttributeError: 'Temporal' object has no attribute '_data'

seaborn/_core/scales.py:400: AttributeError
______________________ TestTemporal.test_label_formatter _______________________

self = <tests._core.test_scales.TestTemporal object at 0x7fe9f19bd100>
t = 0   1972-09-27
1   1975-06-24
2   1980-12-14
Name: x, dtype: datetime64[ns]

    @pytest.mark.skipif(
        Version(mpl.__version__) < Version("3.3.0"),
        reason="Test requires new matplotlib date epoch."
    )
    def test_label_formatter(self, t):

        formatter = mpl.dates.DateFormatter("%Y")
        s = Temporal().label(formatter)
>       a = PseudoAxis(s._setup(t, Coordinate())._matplotlib_scale)

tests/_core/test_scales.py:666:
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
seaborn/_core/scales.py:332: in _setup
    forward, inverse = new._get_transform()
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _

self = Temporal(values=None, norm=None)

    def _get_transform(self):

>       if isinstance(self._data, Series) and self._data.dtype == bool:
E       AttributeError: 'Temporal' object has no attribute '_data'

seaborn/_core/scales.py:400: AttributeError
_______________________ TestTemporal.test_label_concise ________________________

self = <tests._core.test_scales.TestTemporal object at 0x7fe9f1a28b50>
t = 0   1972-09-27
1   1975-06-24
2   1980-12-14
Name: x, dtype: datetime64[ns]
x = 0    1000.0
1    2000.0
2    4000.0
Name: x, dtype: float64

    def test_label_concise(self, t, x):

        ax = mpl.figure.Figure().subplots()
>       Temporal().label(concise=True)._setup(t, Coordinate(), ax.xaxis)

tests/_core/test_scales.py:674:
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
seaborn/_core/scales.py:332: in _setup
    forward, inverse = new._get_transform()
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _

self = Temporal(values=None, norm=None)

    def _get_transform(self):

>       if isinstance(self._data, Series) and self._data.dtype == bool:
E       AttributeError: 'Temporal' object has no attribute '_data'

seaborn/_core/scales.py:400: AttributeError
================================== XFAILURES ===================================
_________________ TestNominal.test_color_numeric_int_float_mix _________________

self = <tests._core.test_scales.TestNominal object at 0x7fe9f19bd0d0>

    @pytest.mark.xfail(reason="Need to sort out float/int order")
    def test_color_numeric_int_float_mix(self):

        z = pd.Series([1, 2], name="z")
        s = Nominal(order=[1.0, 2])._setup(z, Color())
        c1, c2 = color_palette(n_colors=2)
        null = (np.nan, np.nan, np.nan)
>       assert_array_equal(s(z), [c1, null, c2])

tests/_core/test_scales.py:450:
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
seaborn/_core/scales.py:124: in __call__
    trans_data = func(trans_data)
seaborn/_core/properties.py:646: in mapping
    out[use] = np.take(colors, ixs[use], axis=0)
/opt/miniconda3/envs/testbed/lib/python3.9/site-packages/numpy/core/fromnumeric.py:192: in take
    return _wrapfunc(a, 'take', indices, axis=axis, out=out, mode=mode)
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _

obj = array([[0.12156863, 0.46666667, 0.70588235],
       [1.        , 0.49803922, 0.05490196]])
method = 'take', args = (array([2, 3]),)
kwds = {'axis': 0, 'mode': 'raise', 'out': None}
bound = <built-in method take of numpy.ndarray object at 0x7fe9f18637b0>

    def _wrapfunc(obj, method, *args, **kwds):
        bound = getattr(obj, method, None)
        if bound is None:
            return _wrapit(obj, method, *args, **kwds)

        try:
>           return bound(*args, **kwds)
E           IndexError: index 2 is out of bounds for axis 0 with size 2

/opt/miniconda3/envs/testbed/lib/python3.9/site-packages/numpy/core/fromnumeric.py:59: IndexError
=============================== warnings summary ===============================
tests/_core/test_scales.py::TestNominal::test_color_numeric_with_order_subset
  /testbed/seaborn/_core/properties.py:643: RuntimeWarning: invalid value encountered in cast
    ixs = np.asarray(x, np.intp)

tests/_core/test_scales.py::TestNominal::test_object_order_subset
  /testbed/seaborn/_core/properties.py:366: RuntimeWarning: invalid value encountered in cast
    ixs = np.asarray(x, np.intp)

-- Docs: https://docs.pytest.org/en/stable/how-to/capture-warnings.html
==================================== PASSES ====================================
=========================== short test summary info ============================
PASSED tests/_core/test_scales.py::TestContinuous::test_tick_locator_input_check
PASSED tests/_core/test_scales.py::TestContinuous::test_log_tick_every
PASSED tests/_core/test_scales.py::TestContinuous::test_label_type_checks
PASSED tests/_core/test_scales.py::TestNominal::test_coordinate_defaults
PASSED tests/_core/test_scales.py::TestNominal::test_coordinate_with_order
PASSED tests/_core/test_scales.py::TestNominal::test_coordinate_with_subset_order
PASSED tests/_core/test_scales.py::TestNominal::test_coordinate_axis
PASSED tests/_core/test_scales.py::TestNominal::test_coordinate_axis_with_order
PASSED tests/_core/test_scales.py::TestNominal::test_coordinate_axis_with_subset_order
PASSED tests/_core/test_scales.py::TestNominal::test_coordinate_axis_with_category_dtype
PASSED tests/_core/test_scales.py::TestNominal::test_coordinate_numeric_data
PASSED tests/_core/test_scales.py::TestNominal::test_coordinate_numeric_data_with_order
PASSED tests/_core/test_scales.py::TestNominal::test_color_defaults
PASSED tests/_core/test_scales.py::TestNominal::test_color_named_palette
PASSED tests/_core/test_scales.py::TestNominal::test_color_list_palette
PASSED tests/_core/test_scales.py::TestNominal::test_color_dict_palette
PASSED tests/_core/test_scales.py::TestNominal::test_color_numeric_data
PASSED tests/_core/test_scales.py::TestNominal::test_color_numeric_with_order_subset
PASSED tests/_core/test_scales.py::TestNominal::test_color_alpha_in_palette
PASSED tests/_core/test_scales.py::TestNominal::test_color_unknown_palette
PASSED tests/_core/test_scales.py::TestNominal::test_object_defaults
PASSED tests/_core/test_scales.py::TestNominal::test_object_list
PASSED tests/_core/test_scales.py::TestNominal::test_object_dict
PASSED tests/_core/test_scales.py::TestNominal::test_object_order
PASSED tests/_core/test_scales.py::TestNominal::test_object_order_subset
PASSED tests/_core/test_scales.py::TestNominal::test_objects_that_are_weird
PASSED tests/_core/test_scales.py::TestNominal::test_alpha_default
PASSED tests/_core/test_scales.py::TestNominal::test_fill
PASSED tests/_core/test_scales.py::TestNominal::test_fill_dict
PASSED tests/_core/test_scales.py::TestNominal::test_fill_nunique_warning
PASSED tests/_core/test_scales.py::TestNominal::test_interval_defaults
PASSED tests/_core/test_scales.py::TestNominal::test_interval_tuple
PASSED tests/_core/test_scales.py::TestNominal::test_interval_tuple_numeric
PASSED tests/_core/test_scales.py::TestNominal::test_interval_list
PASSED tests/_core/test_scales.py::TestNominal::test_interval_dict
PASSED tests/_core/test_scales.py::TestNominal::test_interval_with_transform
PASSED tests/_core/test_scales.py::TestNominal::test_empty_data
XFAIL tests/_core/test_scales.py::TestNominal::test_color_numeric_int_float_mix - Need to sort out float/int order
FAILED tests/_core/test_scales.py::TestContinuous::test_coordinate_defaults
FAILED tests/_core/test_scales.py::TestContinuous::test_coordinate_transform
FAILED tests/_core/test_scales.py::TestContinuous::test_coordinate_transform_with_parameter
FAILED tests/_core/test_scales.py::TestContinuous::test_coordinate_transform_error
FAILED tests/_core/test_scales.py::TestContinuous::test_interval_defaults - A...
FAILED tests/_core/test_scales.py::TestContinuous::test_interval_with_range
FAILED tests/_core/test_scales.py::TestContinuous::test_interval_with_norm - ...
FAILED tests/_core/test_scales.py::TestContinuous::test_interval_with_range_norm_and_transform
FAILED tests/_core/test_scales.py::TestContinuous::test_color_defaults - Attr...
FAILED tests/_core/test_scales.py::TestContinuous::test_color_named_values - ...
FAILED tests/_core/test_scales.py::TestContinuous::test_color_tuple_values - ...
FAILED tests/_core/test_scales.py::TestContinuous::test_color_callable_values
FAILED tests/_core/test_scales.py::TestContinuous::test_color_with_norm - Att...
FAILED tests/_core/test_scales.py::TestContinuous::test_color_with_transform
FAILED tests/_core/test_scales.py::TestContinuous::test_tick_locator - Attrib...
FAILED tests/_core/test_scales.py::TestContinuous::test_tick_upto - Attribute...
FAILED tests/_core/test_scales.py::TestContinuous::test_tick_every - Attribut...
FAILED tests/_core/test_scales.py::TestContinuous::test_tick_every_between - ...
FAILED tests/_core/test_scales.py::TestContinuous::test_tick_at - AttributeEr...
FAILED tests/_core/test_scales.py::TestContinuous::test_tick_count - Attribut...
FAILED tests/_core/test_scales.py::TestContinuous::test_tick_count_between - ...
FAILED tests/_core/test_scales.py::TestContinuous::test_tick_minor - Attribut...
FAILED tests/_core/test_scales.py::TestContinuous::test_log_tick_default - At...
FAILED tests/_core/test_scales.py::TestContinuous::test_log_tick_upto - Attri...
FAILED tests/_core/test_scales.py::TestContinuous::test_log_tick_count - Attr...
FAILED tests/_core/test_scales.py::TestContinuous::test_log_tick_format_disabled
FAILED tests/_core/test_scales.py::TestContinuous::test_symlog_tick_default
FAILED tests/_core/test_scales.py::TestContinuous::test_label_formatter - Att...
FAILED tests/_core/test_scales.py::TestContinuous::test_label_like_pattern - ...
FAILED tests/_core/test_scales.py::TestContinuous::test_label_like_string - A...
FAILED tests/_core/test_scales.py::TestContinuous::test_label_like_function
FAILED tests/_core/test_scales.py::TestContinuous::test_label_base - Attribut...
FAILED tests/_core/test_scales.py::TestContinuous::test_label_unit - Attribut...
FAILED tests/_core/test_scales.py::TestContinuous::test_label_unit_with_sep
FAILED tests/_core/test_scales.py::TestContinuous::test_label_empty_unit - At...
FAILED tests/_core/test_scales.py::TestContinuous::test_label_base_from_transform
FAILED tests/_core/test_scales.py::TestContinuous::test_color_with_boolean_data
FAILED tests/_core/test_scales.py::TestTemporal::test_coordinate_defaults - A...
FAILED tests/_core/test_scales.py::TestTemporal::test_interval_defaults - Att...
FAILED tests/_core/test_scales.py::TestTemporal::test_interval_with_range - A...
FAILED tests/_core/test_scales.py::TestTemporal::test_interval_with_norm - At...
FAILED tests/_core/test_scales.py::TestTemporal::test_color_defaults - Attrib...
FAILED tests/_core/test_scales.py::TestTemporal::test_color_named_values - At...
FAILED tests/_core/test_scales.py::TestTemporal::test_coordinate_axis - Attri...
FAILED tests/_core/test_scales.py::TestTemporal::test_tick_locator - Attribut...
FAILED tests/_core/test_scales.py::TestTemporal::test_tick_upto - AttributeEr...
FAILED tests/_core/test_scales.py::TestTemporal::test_label_formatter - Attri...
FAILED tests/_core/test_scales.py::TestTemporal::test_label_concise - Attribu...
============= 48 failed, 37 passed, 1 xfailed, 2 warnings in 2.91s =============

