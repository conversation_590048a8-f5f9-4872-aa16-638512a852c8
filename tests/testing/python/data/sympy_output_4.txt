sympy/combinatorics/tests/test_permutations.py[0] Traceback (most recent call last):
  File "/testbed/sympy/utilities/runtests.py", line 1079, in test_file
    exec_(code, gl)
  File "/testbed/sympy/combinatorics/tests/test_permutations.py", line 5, in <module>
    from sympy.combinatorics.permutations import (Permutation, _af_parity,
  File "/testbed/sympy/combinatorics/__init__.py", line 7, in <module>
    from sympy.combinatorics.polyhedron import (Polyhedron, tetrahedron, cube,
  File "/testbed/sympy/combinatorics/polyhedron.py", line 824, in <module>
    dodecahedron_faces, icosahedron_faces) = _pgroup_calcs()
  File "/testbed/sympy/combinatorics/polyhedron.py", line 724, in _pgroup_calcs
    _c_pgroup = [Perm(p) for p in
  File "/testbed/sympy/combinatorics/polyhedron.py", line 724, in <listcomp>
    _c_pgroup = [Perm(p) for p in
  File "/testbed/sympy/combinatorics/permutations.py", line 900, in __new__
    for i in range(len(ci)):
TypeError: object of type 'int' has no len()

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "<string>", line 1, in <module>
  File "/testbed/sympy/utilities/runtests.py", line 536, in _test
    return int(not t.test(sort=sort, timeout=timeout,
  File "/testbed/sympy/utilities/runtests.py", line 1013, in test
    self.test_file(f, sort, timeout, slow, enhance_asserts)
  File "/testbed/sympy/utilities/runtests.py", line 1086, in test_file
    reporter.test_exception(sys.exc_info())
  File "/testbed/sympy/utilities/runtests.py", line 2217, in test_exception
    self._exceptions.append((self._active_file, self._active_f, exc_info))
AttributeError: 'PyTestReporter' object has no attribute '_active_file'

