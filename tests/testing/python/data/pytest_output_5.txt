============================= test session starts ==============================
platform linux -- Python 3.9.19, pytest-6.0.0rc2.dev33+g7f7a36478, py-1.11.0, pluggy-0.13.1
rootdir: /testbed, configfile: pyproject.toml
collected 81 items

testing/test_skipping.py ............................................... [ 58%]
...................F..............                                       [100%]

=================================== FAILURES ===================================
____________________ test_errors_in_xfail_skip_expressions _____________________

testdir = <Testdir local('/tmp/pytest-of-root/pytest-12/test_errors_in_xfail_skip_expressions0')>

    def test_errors_in_xfail_skip_expressions(testdir) -> None:
        testdir.makepyfile(
            """
            import pytest
            @pytest.mark.skipif("asd")
            def test_nameerror():
                pass
            @pytest.mark.xfail("syntax error")
            def test_syntax():
                pass

            def test_func():
                pass
        """
        )
        result = testdir.runpytest()
        markline = "                ^"
        pypy_version_info = getattr(sys, "pypy_version_info", None)
        if pypy_version_info is not None and pypy_version_info < (6,):
            markline = markline[5:]
        elif sys.version_info >= (3, 8) or hasattr(sys, "pypy_version_info"):
            markline = markline[4:]
>       result.stdout.fnmatch_lines(
            [
                "*ERROR*test_nameerror*",
                "*evaluating*skipif*condition*",
                "*asd*",
                "*ERROR*test_syntax*",
                "*evaluating*xfail*condition*",
                "    syntax error",
                markline,
                "SyntaxError: invalid syntax",
                "*1 pass*2 errors*",
            ]
        )
E       Failed: nomatch: '*ERROR*test_nameerror*'
E           and: '============================= test session starts =============================='
E           and: 'platform linux -- Python 3.9.19, pytest-6.0.0rc2.dev33+g7f7a36478, py-1.11.0, pluggy-0.13.1'
E           and: 'rootdir: /tmp/pytest-of-root/pytest-12/test_errors_in_xfail_skip_expressions0'
E           and: 'collected 3 items'
E           and: ''
E           and: 'test_errors_in_xfail_skip_expressions.py E'
E           and: 'INTERNALERROR> Traceback (most recent call last):'
E           and: 'INTERNALERROR>   File "/testbed/src/_pytest/skipping.py", line 108, in evaluate_condition'
E           and: 'INTERNALERROR>     condition_code = compile(condition, filename, "eval")'
E           and: 'INTERNALERROR>   File "<xfail condition>", line 1'
E           and: 'INTERNALERROR>     syntax error'
E           and: 'INTERNALERROR>            ^'
E           and: 'INTERNALERROR> SyntaxError: unexpected EOF while parsing'
E           and: 'INTERNALERROR> '
E           and: 'INTERNALERROR> During handling of the above exception, another exception occurred:'
E           and: 'INTERNALERROR> '
E           and: 'INTERNALERROR> Traceback (most recent call last):'
E           and: 'INTERNALERROR>   File "/testbed/src/_pytest/main.py", line 240, in wrap_session'
E           and: 'INTERNALERROR>     session.exitstatus = doit(config, session) or 0'
E           and: 'INTERNALERROR>   File "/testbed/src/_pytest/main.py", line 296, in _main'
E           and: 'INTERNALERROR>     config.hook.pytest_runtestloop(session=session)'
E           and: 'INTERNALERROR>   File "/opt/miniconda3/envs/testbed/lib/python3.9/site-packages/pluggy/hooks.py", line 286, in __call__'
E           and: 'INTERNALERROR>     return self._hookexec(self, self.get_hookimpls(), kwargs)'
E           and: 'INTERNALERROR>   File "/opt/miniconda3/envs/testbed/lib/python3.9/site-packages/pluggy/manager.py", line 93, in _hookexec'
E           and: 'INTERNALERROR>     return self._inner_hookexec(hook, methods, kwargs)'
E           and: 'INTERNALERROR>   File "/opt/miniconda3/envs/testbed/lib/python3.9/site-packages/pluggy/manager.py", line 337, in traced_hookexec'
E           and: 'INTERNALERROR>     return outcome.get_result()'
E           and: 'INTERNALERROR>   File "/opt/miniconda3/envs/testbed/lib/python3.9/site-packages/pluggy/callers.py", line 80, in get_result'
E           and: 'INTERNALERROR>     raise ex[1].with_traceback(ex[2])'
E           and: 'INTERNALERROR>   File "/opt/miniconda3/envs/testbed/lib/python3.9/site-packages/pluggy/callers.py", line 52, in from_call'
E           and: 'INTERNALERROR>     result = func()'
E           and: 'INTERNALERROR>   File "/opt/miniconda3/envs/testbed/lib/python3.9/site-packages/pluggy/manager.py", line 335, in <lambda>'
E           and: 'INTERNALERROR>     outcome = _Result.from_call(lambda: oldcall(hook, hook_impls, kwargs))'
E           and: 'INTERNALERROR>   File "/opt/miniconda3/envs/testbed/lib/python3.9/site-packages/pluggy/manager.py", line 84, in <lambda>'
E           and: 'INTERNALERROR>     self._inner_hookexec = lambda hook, methods, kwargs: hook.multicall('
E           and: 'INTERNALERROR>   File "/opt/miniconda3/envs/testbed/lib/python3.9/site-packages/pluggy/callers.py", line 208, in _multicall'
E           and: 'INTERNALERROR>     return outcome.get_result()'
E           and: 'INTERNALERROR>   File "/opt/miniconda3/envs/testbed/lib/python3.9/site-packages/pluggy/callers.py", line 80, in get_result'
E           and: 'INTERNALERROR>     raise ex[1].with_traceback(ex[2])'
E           and: 'INTERNALERROR>   File "/opt/miniconda3/envs/testbed/lib/python3.9/site-packages/pluggy/callers.py", line 187, in _multicall'
E           and: 'INTERNALERROR>     res = hook_impl.function(*args)'
E           and: 'INTERNALERROR>   File "/testbed/src/_pytest/main.py", line 321, in pytest_runtestloop'
E           and: 'INTERNALERROR>     item.config.hook.pytest_runtest_protocol(item=item, nextitem=nextitem)'
E           and: 'INTERNALERROR>   File "/opt/miniconda3/envs/testbed/lib/python3.9/site-packages/pluggy/hooks.py", line 286, in __call__'
E           and: 'INTERNALERROR>     return self._hookexec(self, self.get_hookimpls(), kwargs)'
E           and: 'INTERNALERROR>   File "/opt/miniconda3/envs/testbed/lib/python3.9/site-packages/pluggy/manager.py", line 93, in _hookexec'
E           and: 'INTERNALERROR>     return self._inner_hookexec(hook, methods, kwargs)'
E           and: 'INTERNALERROR>   File "/opt/miniconda3/envs/testbed/lib/python3.9/site-packages/pluggy/manager.py", line 337, in traced_hookexec'
E           and: 'INTERNALERROR>     return outcome.get_result()'
E           and: 'INTERNALERROR>   File "/opt/miniconda3/envs/testbed/lib/python3.9/site-packages/pluggy/callers.py", line 80, in get_result'
E           and: 'INTERNALERROR>     raise ex[1].with_traceback(ex[2])'
E           and: 'INTERNALERROR>   File "/opt/miniconda3/envs/testbed/lib/python3.9/site-packages/pluggy/callers.py", line 52, in from_call'
E           and: 'INTERNALERROR>     result = func()'
E           and: 'INTERNALERROR>   File "/opt/miniconda3/envs/testbed/lib/python3.9/site-packages/pluggy/manager.py", line 335, in <lambda>'
E           and: 'INTERNALERROR>     outcome = _Result.from_call(lambda: oldcall(hook, hook_impls, kwargs))'
E           and: 'INTERNALERROR>   File "/opt/miniconda3/envs/testbed/lib/python3.9/site-packages/pluggy/manager.py", line 84, in <lambda>'
E           and: 'INTERNALERROR>     self._inner_hookexec = lambda hook, methods, kwargs: hook.multicall('
E           and: 'INTERNALERROR>   File "/opt/miniconda3/envs/testbed/lib/python3.9/site-packages/pluggy/callers.py", line 208, in _multicall'
E           and: 'INTERNALERROR>     return outcome.get_result()'
E           and: 'INTERNALERROR>   File "/opt/miniconda3/envs/testbed/lib/python3.9/site-packages/pluggy/callers.py", line 80, in get_result'
E           and: 'INTERNALERROR>     raise ex[1].with_traceback(ex[2])'
E           and: 'INTERNALERROR>   File "/opt/miniconda3/envs/testbed/lib/python3.9/site-packages/pluggy/callers.py", line 187, in _multicall'
E           and: 'INTERNALERROR>     res = hook_impl.function(*args)'
E           and: 'INTERNALERROR>   File "/testbed/src/_pytest/runner.py", line 100, in pytest_runtest_protocol'
E           and: 'INTERNALERROR>     runtestprotocol(item, nextitem=nextitem)'
E           and: 'INTERNALERROR>   File "/testbed/src/_pytest/runner.py", line 111, in runtestprotocol'
E           and: 'INTERNALERROR>     rep = call_and_report(item, "setup", log)'
E           and: 'INTERNALERROR>   File "/testbed/src/_pytest/runner.py", line 209, in call_and_report'
E           and: 'INTERNALERROR>     report = hook.pytest_runtest_makereport(item=item, call=call)  # type: TestReport'
E           and: 'INTERNALERROR>   File "/opt/miniconda3/envs/testbed/lib/python3.9/site-packages/pluggy/hooks.py", line 286, in __call__'
E           and: 'INTERNALERROR>     return self._hookexec(self, self.get_hookimpls(), kwargs)'
E           and: 'INTERNALERROR>   File "/opt/miniconda3/envs/testbed/lib/python3.9/site-packages/pluggy/manager.py", line 93, in _hookexec'
E           and: 'INTERNALERROR>     return self._inner_hookexec(hook, methods, kwargs)'
E           and: 'INTERNALERROR>   File "/opt/miniconda3/envs/testbed/lib/python3.9/site-packages/pluggy/manager.py", line 337, in traced_hookexec'
E           and: 'INTERNALERROR>     return outcome.get_result()'
E           and: 'INTERNALERROR>   File "/opt/miniconda3/envs/testbed/lib/python3.9/site-packages/pluggy/callers.py", line 80, in get_result'
E           and: 'INTERNALERROR>     raise ex[1].with_traceback(ex[2])'
E           and: 'INTERNALERROR>   File "/opt/miniconda3/envs/testbed/lib/python3.9/site-packages/pluggy/callers.py", line 52, in from_call'
E           and: 'INTERNALERROR>     result = func()'
E           and: 'INTERNALERROR>   File "/opt/miniconda3/envs/testbed/lib/python3.9/site-packages/pluggy/manager.py", line 335, in <lambda>'
E           and: 'INTERNALERROR>     outcome = _Result.from_call(lambda: oldcall(hook, hook_impls, kwargs))'
E           and: 'INTERNALERROR>   File "/opt/miniconda3/envs/testbed/lib/python3.9/site-packages/pluggy/manager.py", line 84, in <lambda>'
E           and: 'INTERNALERROR>     self._inner_hookexec = lambda hook, methods, kwargs: hook.multicall('
E           and: 'INTERNALERROR>   File "/opt/miniconda3/envs/testbed/lib/python3.9/site-packages/pluggy/callers.py", line 203, in _multicall'
E           and: 'INTERNALERROR>     gen.send(outcome)'
E           and: 'INTERNALERROR>   File "/testbed/src/_pytest/skipping.py", line 266, in pytest_runtest_makereport'
E           and: 'INTERNALERROR>     xfailed = evaluate_xfail_marks(item)'
E           and: 'INTERNALERROR>   File "/testbed/src/_pytest/skipping.py", line 218, in evaluate_xfail_marks'
E           and: 'INTERNALERROR>     result, reason = evaluate_condition(item, mark, condition)'
E           and: 'INTERNALERROR>   File "/testbed/src/_pytest/skipping.py", line 117, in evaluate_condition'
E           and: 'INTERNALERROR>     fail("\\n".join(msglines), pytrace=False)'
E           and: 'INTERNALERROR>   File "/testbed/src/_pytest/outcomes.py", line 156, in fail'
E           and: 'INTERNALERROR>     raise Failed(msg=msg, pytrace=pytrace)'
E           and: "INTERNALERROR> Failed: Error evaluating 'xfail' condition"
E           and: 'INTERNALERROR>     syntax error'
E           and: 'INTERNALERROR>             ^'
E           and: 'INTERNALERROR> SyntaxError: invalid syntax'
E           and: ''
E           and: '=============================== 1 error in 0.01s ==============================='
E       remains unmatched: '*ERROR*test_nameerror*'

/testbed/testing/test_skipping.py:969: Failed
----------------------------- Captured stdout call -----------------------------
============================= test session starts ==============================
platform linux -- Python 3.9.19, pytest-6.0.0rc2.dev33+g7f7a36478, py-1.11.0, pluggy-0.13.1
rootdir: /tmp/pytest-of-root/pytest-12/test_errors_in_xfail_skip_expressions0
collected 3 items

test_errors_in_xfail_skip_expressions.py E
INTERNALERROR> Traceback (most recent call last):
INTERNALERROR>   File "/testbed/src/_pytest/skipping.py", line 108, in evaluate_condition
INTERNALERROR>     condition_code = compile(condition, filename, "eval")
INTERNALERROR>   File "<xfail condition>", line 1
INTERNALERROR>     syntax error
INTERNALERROR>            ^
INTERNALERROR> SyntaxError: unexpected EOF while parsing
INTERNALERROR>
INTERNALERROR> During handling of the above exception, another exception occurred:
INTERNALERROR>
INTERNALERROR> Traceback (most recent call last):
INTERNALERROR>   File "/testbed/src/_pytest/main.py", line 240, in wrap_session
INTERNALERROR>     session.exitstatus = doit(config, session) or 0
INTERNALERROR>   File "/testbed/src/_pytest/main.py", line 296, in _main
INTERNALERROR>     config.hook.pytest_runtestloop(session=session)
INTERNALERROR>   File "/opt/miniconda3/envs/testbed/lib/python3.9/site-packages/pluggy/hooks.py", line 286, in __call__
INTERNALERROR>     return self._hookexec(self, self.get_hookimpls(), kwargs)
INTERNALERROR>   File "/opt/miniconda3/envs/testbed/lib/python3.9/site-packages/pluggy/manager.py", line 93, in _hookexec
INTERNALERROR>     return self._inner_hookexec(hook, methods, kwargs)
INTERNALERROR>   File "/opt/miniconda3/envs/testbed/lib/python3.9/site-packages/pluggy/manager.py", line 337, in traced_hookexec
INTERNALERROR>     return outcome.get_result()
INTERNALERROR>   File "/opt/miniconda3/envs/testbed/lib/python3.9/site-packages/pluggy/callers.py", line 80, in get_result
INTERNALERROR>     raise ex[1].with_traceback(ex[2])
INTERNALERROR>   File "/opt/miniconda3/envs/testbed/lib/python3.9/site-packages/pluggy/callers.py", line 52, in from_call
INTERNALERROR>     result = func()
INTERNALERROR>   File "/opt/miniconda3/envs/testbed/lib/python3.9/site-packages/pluggy/manager.py", line 335, in <lambda>
INTERNALERROR>     outcome = _Result.from_call(lambda: oldcall(hook, hook_impls, kwargs))
INTERNALERROR>   File "/opt/miniconda3/envs/testbed/lib/python3.9/site-packages/pluggy/manager.py", line 84, in <lambda>
INTERNALERROR>     self._inner_hookexec = lambda hook, methods, kwargs: hook.multicall(
INTERNALERROR>   File "/opt/miniconda3/envs/testbed/lib/python3.9/site-packages/pluggy/callers.py", line 208, in _multicall
INTERNALERROR>     return outcome.get_result()
INTERNALERROR>   File "/opt/miniconda3/envs/testbed/lib/python3.9/site-packages/pluggy/callers.py", line 80, in get_result
INTERNALERROR>     raise ex[1].with_traceback(ex[2])
INTERNALERROR>   File "/opt/miniconda3/envs/testbed/lib/python3.9/site-packages/pluggy/callers.py", line 187, in _multicall
INTERNALERROR>     res = hook_impl.function(*args)
INTERNALERROR>   File "/testbed/src/_pytest/main.py", line 321, in pytest_runtestloop
INTERNALERROR>     item.config.hook.pytest_runtest_protocol(item=item, nextitem=nextitem)
INTERNALERROR>   File "/opt/miniconda3/envs/testbed/lib/python3.9/site-packages/pluggy/hooks.py", line 286, in __call__
INTERNALERROR>     return self._hookexec(self, self.get_hookimpls(), kwargs)
INTERNALERROR>   File "/opt/miniconda3/envs/testbed/lib/python3.9/site-packages/pluggy/manager.py", line 93, in _hookexec
INTERNALERROR>     return self._inner_hookexec(hook, methods, kwargs)
INTERNALERROR>   File "/opt/miniconda3/envs/testbed/lib/python3.9/site-packages/pluggy/manager.py", line 337, in traced_hookexec
INTERNALERROR>     return outcome.get_result()
INTERNALERROR>   File "/opt/miniconda3/envs/testbed/lib/python3.9/site-packages/pluggy/callers.py", line 80, in get_result
INTERNALERROR>     raise ex[1].with_traceback(ex[2])
INTERNALERROR>   File "/opt/miniconda3/envs/testbed/lib/python3.9/site-packages/pluggy/callers.py", line 52, in from_call
INTERNALERROR>     result = func()
INTERNALERROR>   File "/opt/miniconda3/envs/testbed/lib/python3.9/site-packages/pluggy/manager.py", line 335, in <lambda>
INTERNALERROR>     outcome = _Result.from_call(lambda: oldcall(hook, hook_impls, kwargs))
INTERNALERROR>   File "/opt/miniconda3/envs/testbed/lib/python3.9/site-packages/pluggy/manager.py", line 84, in <lambda>
INTERNALERROR>     self._inner_hookexec = lambda hook, methods, kwargs: hook.multicall(
INTERNALERROR>   File "/opt/miniconda3/envs/testbed/lib/python3.9/site-packages/pluggy/callers.py", line 208, in _multicall
INTERNALERROR>     return outcome.get_result()
INTERNALERROR>   File "/opt/miniconda3/envs/testbed/lib/python3.9/site-packages/pluggy/callers.py", line 80, in get_result
INTERNALERROR>     raise ex[1].with_traceback(ex[2])
INTERNALERROR>   File "/opt/miniconda3/envs/testbed/lib/python3.9/site-packages/pluggy/callers.py", line 187, in _multicall
INTERNALERROR>     res = hook_impl.function(*args)
INTERNALERROR>   File "/testbed/src/_pytest/runner.py", line 100, in pytest_runtest_protocol
INTERNALERROR>     runtestprotocol(item, nextitem=nextitem)
INTERNALERROR>   File "/testbed/src/_pytest/runner.py", line 111, in runtestprotocol
INTERNALERROR>     rep = call_and_report(item, "setup", log)
INTERNALERROR>   File "/testbed/src/_pytest/runner.py", line 209, in call_and_report
INTERNALERROR>     report = hook.pytest_runtest_makereport(item=item, call=call)  # type: TestReport
INTERNALERROR>   File "/opt/miniconda3/envs/testbed/lib/python3.9/site-packages/pluggy/hooks.py", line 286, in __call__
INTERNALERROR>     return self._hookexec(self, self.get_hookimpls(), kwargs)
INTERNALERROR>   File "/opt/miniconda3/envs/testbed/lib/python3.9/site-packages/pluggy/manager.py", line 93, in _hookexec
INTERNALERROR>     return self._inner_hookexec(hook, methods, kwargs)
INTERNALERROR>   File "/opt/miniconda3/envs/testbed/lib/python3.9/site-packages/pluggy/manager.py", line 337, in traced_hookexec
INTERNALERROR>     return outcome.get_result()
INTERNALERROR>   File "/opt/miniconda3/envs/testbed/lib/python3.9/site-packages/pluggy/callers.py", line 80, in get_result
INTERNALERROR>     raise ex[1].with_traceback(ex[2])
INTERNALERROR>   File "/opt/miniconda3/envs/testbed/lib/python3.9/site-packages/pluggy/callers.py", line 52, in from_call
INTERNALERROR>     result = func()
INTERNALERROR>   File "/opt/miniconda3/envs/testbed/lib/python3.9/site-packages/pluggy/manager.py", line 335, in <lambda>
INTERNALERROR>     outcome = _Result.from_call(lambda: oldcall(hook, hook_impls, kwargs))
INTERNALERROR>   File "/opt/miniconda3/envs/testbed/lib/python3.9/site-packages/pluggy/manager.py", line 84, in <lambda>
INTERNALERROR>     self._inner_hookexec = lambda hook, methods, kwargs: hook.multicall(
INTERNALERROR>   File "/opt/miniconda3/envs/testbed/lib/python3.9/site-packages/pluggy/callers.py", line 203, in _multicall
INTERNALERROR>     gen.send(outcome)
INTERNALERROR>   File "/testbed/src/_pytest/skipping.py", line 266, in pytest_runtest_makereport
INTERNALERROR>     xfailed = evaluate_xfail_marks(item)
INTERNALERROR>   File "/testbed/src/_pytest/skipping.py", line 218, in evaluate_xfail_marks
INTERNALERROR>     result, reason = evaluate_condition(item, mark, condition)
INTERNALERROR>   File "/testbed/src/_pytest/skipping.py", line 117, in evaluate_condition
INTERNALERROR>     fail("\n".join(msglines), pytrace=False)
INTERNALERROR>   File "/testbed/src/_pytest/outcomes.py", line 156, in fail
INTERNALERROR>     raise Failed(msg=msg, pytrace=pytrace)
INTERNALERROR> Failed: Error evaluating 'xfail' condition
INTERNALERROR>     syntax error
INTERNALERROR>             ^
INTERNALERROR> SyntaxError: invalid syntax

=============================== 1 error in 0.01s ===============================
==================================== PASSES ====================================
________________________ TestEvaluation.test_no_marker _________________________
----------------------------- Captured stdout call -----------------------------
============================= test session starts ==============================
platform linux -- Python 3.9.19, pytest-6.0.0rc2.dev33+g7f7a36478, py-1.11.0, pluggy-0.13.1
rootdir: /tmp/pytest-of-root/pytest-12/test_no_marker0
collected 0 items

============================ no tests ran in 0.00s =============================
___________________ TestEvaluation.test_marked_xfail_no_args ___________________
----------------------------- Captured stdout call -----------------------------
============================= test session starts ==============================
platform linux -- Python 3.9.19, pytest-6.0.0rc2.dev33+g7f7a36478, py-1.11.0, pluggy-0.13.1
rootdir: /tmp/pytest-of-root/pytest-12/test_marked_xfail_no_args0
collected 0 items

============================ no tests ran in 0.00s =============================
__________________ TestEvaluation.test_marked_skipif_no_args ___________________
----------------------------- Captured stdout call -----------------------------
============================= test session starts ==============================
platform linux -- Python 3.9.19, pytest-6.0.0rc2.dev33+g7f7a36478, py-1.11.0, pluggy-0.13.1
rootdir: /tmp/pytest-of-root/pytest-12/test_marked_skipif_no_args0
collected 0 items

============================ no tests ran in 0.00s =============================
______________________ TestEvaluation.test_marked_one_arg ______________________
----------------------------- Captured stdout call -----------------------------
============================= test session starts ==============================
platform linux -- Python 3.9.19, pytest-6.0.0rc2.dev33+g7f7a36478, py-1.11.0, pluggy-0.13.1
rootdir: /tmp/pytest-of-root/pytest-12/test_marked_one_arg0
collected 0 items

============================ no tests ran in 0.00s =============================
________________ TestEvaluation.test_marked_one_arg_with_reason ________________
----------------------------- Captured stdout call -----------------------------
============================= test session starts ==============================
platform linux -- Python 3.9.19, pytest-6.0.0rc2.dev33+g7f7a36478, py-1.11.0, pluggy-0.13.1
rootdir: /tmp/pytest-of-root/pytest-12/test_marked_one_arg_with_reason0
collected 0 items

============================ no tests ran in 0.00s =============================
___________________ TestEvaluation.test_marked_one_arg_twice ___________________
----------------------------- Captured stdout call -----------------------------
============================= test session starts ==============================
platform linux -- Python 3.9.19, pytest-6.0.0rc2.dev33+g7f7a36478, py-1.11.0, pluggy-0.13.1
rootdir: /tmp/pytest-of-root/pytest-12/test_marked_one_arg_twice0
collected 0 items

============================ no tests ran in 0.00s =============================
============================= test session starts ==============================
platform linux -- Python 3.9.19, pytest-6.0.0rc2.dev33+g7f7a36478, py-1.11.0, pluggy-0.13.1
rootdir: /tmp/pytest-of-root/pytest-12/test_marked_one_arg_twice0
collected 0 items

============================ no tests ran in 0.00s =============================
__________________ TestEvaluation.test_marked_one_arg_twice2 ___________________
----------------------------- Captured stdout call -----------------------------
============================= test session starts ==============================
platform linux -- Python 3.9.19, pytest-6.0.0rc2.dev33+g7f7a36478, py-1.11.0, pluggy-0.13.1
rootdir: /tmp/pytest-of-root/pytest-12/test_marked_one_arg_twice20
collected 0 items

============================ no tests ran in 0.00s =============================
________ TestEvaluation.test_marked_skipif_with_boolean_without_reason _________
----------------------------- Captured stdout call -----------------------------
============================= test session starts ==============================
platform linux -- Python 3.9.19, pytest-6.0.0rc2.dev33+g7f7a36478, py-1.11.0, pluggy-0.13.1
rootdir: /tmp/pytest-of-root/pytest-12/test_marked_skipif_with_boolean_without_reason0
collected 0 items

============================ no tests ran in 0.00s =============================
____________ TestEvaluation.test_marked_skipif_with_invalid_boolean ____________
----------------------------- Captured stdout call -----------------------------
============================= test session starts ==============================
platform linux -- Python 3.9.19, pytest-6.0.0rc2.dev33+g7f7a36478, py-1.11.0, pluggy-0.13.1
rootdir: /tmp/pytest-of-root/pytest-12/test_marked_skipif_with_invalid_boolean0
collected 0 items

============================ no tests ran in 0.00s =============================
_______________________ TestEvaluation.test_skipif_class _______________________
----------------------------- Captured stdout call -----------------------------
============================= test session starts ==============================
platform linux -- Python 3.9.19, pytest-6.0.0rc2.dev33+g7f7a36478, py-1.11.0, pluggy-0.13.1
rootdir: /tmp/pytest-of-root/pytest-12/test_skipif_class0
collected 0 items

============================ no tests ran in 0.00s =============================
______________________ TestXFail.test_xfail_simple[True] _______________________
----------------------------- Captured stdout call -----------------------------
============================= test session starts ==============================
platform linux -- Python 3.9.19, pytest-6.0.0rc2.dev33+g7f7a36478, py-1.11.0, pluggy-0.13.1
rootdir: /tmp/pytest-of-root/pytest-12/test_xfail_simple0
collected 0 items

============================ no tests ran in 0.00s =============================
______________________ TestXFail.test_xfail_simple[False] ______________________
----------------------------- Captured stdout call -----------------------------
============================= test session starts ==============================
platform linux -- Python 3.9.19, pytest-6.0.0rc2.dev33+g7f7a36478, py-1.11.0, pluggy-0.13.1
rootdir: /tmp/pytest-of-root/pytest-12/test_xfail_simple1
collected 0 items

============================ no tests ran in 0.00s =============================
_________________________ TestXFail.test_xfail_xpassed _________________________
----------------------------- Captured stdout call -----------------------------
============================= test session starts ==============================
platform linux -- Python 3.9.19, pytest-6.0.0rc2.dev33+g7f7a36478, py-1.11.0, pluggy-0.13.1
rootdir: /tmp/pytest-of-root/pytest-12/test_xfail_xpassed0
collected 0 items

============================ no tests ran in 0.00s =============================
_____________________ TestXFail.test_xfail_using_platform ______________________
----------------------------- Captured stdout call -----------------------------
============================= test session starts ==============================
platform linux -- Python 3.9.19, pytest-6.0.0rc2.dev33+g7f7a36478, py-1.11.0, pluggy-0.13.1
rootdir: /tmp/pytest-of-root/pytest-12/test_xfail_using_platform0
collected 0 items

============================ no tests ran in 0.00s =============================
_____________________ TestXFail.test_xfail_xpassed_strict ______________________
----------------------------- Captured stdout call -----------------------------
============================= test session starts ==============================
platform linux -- Python 3.9.19, pytest-6.0.0rc2.dev33+g7f7a36478, py-1.11.0, pluggy-0.13.1
rootdir: /tmp/pytest-of-root/pytest-12/test_xfail_xpassed_strict0
collected 0 items

============================ no tests ran in 0.00s =============================
_______________________ TestXFail.test_xfail_run_anyway ________________________
----------------------------- Captured stdout call -----------------------------
============================= test session starts ==============================
platform linux -- Python 3.9.19, pytest-6.0.0rc2.dev33+g7f7a36478, py-1.11.0, pluggy-0.13.1
rootdir: /tmp/pytest-of-root/pytest-12/test_xfail_run_anyway0
collected 2 items

test_xfail_run_anyway.py F.                                              [100%]

=================================== FAILURES ===================================
__________________________________ test_func ___________________________________

    @pytest.mark.xfail
    def test_func():
>       assert 0
E       assert 0

test_xfail_run_anyway.py:4: AssertionError
=========================== short test summary info ============================
FAILED test_xfail_run_anyway.py::test_func - assert 0
========================= 1 failed, 1 passed in 0.01s ==========================
________ TestXFail.test_xfail_run_with_skip_mark[test_input0-expected0] ________
----------------------------- Captured stdout call -----------------------------
============================= test session starts ==============================
platform linux -- Python 3.9.19, pytest-6.0.0rc2.dev33+g7f7a36478, py-1.11.0, pluggy-0.13.1
rootdir: /tmp/pytest-of-root/pytest-12/test_xfail_run_with_skip_mark0
collected 1 item

test_sample.py s                                                         [100%]

=========================== short test summary info ============================
SKIPPED [1] test_sample.py:2: unconditional skip
============================== 1 skipped in 0.00s ==============================
________ TestXFail.test_xfail_run_with_skip_mark[test_input1-expected1] ________
----------------------------- Captured stdout call -----------------------------
============================= test session starts ==============================
platform linux -- Python 3.9.19, pytest-6.0.0rc2.dev33+g7f7a36478, py-1.11.0, pluggy-0.13.1
rootdir: /tmp/pytest-of-root/pytest-12/test_xfail_run_with_skip_mark1
collected 1 item

test_sample.py s                                                         [100%]

=========================== short test summary info ============================
SKIPPED [1] test_sample.py:2: unconditional skip
============================== 1 skipped in 0.00s ==============================
___________________ TestXFail.test_xfail_evalfalse_but_fails ___________________
----------------------------- Captured stdout call -----------------------------
============================= test session starts ==============================
platform linux -- Python 3.9.19, pytest-6.0.0rc2.dev33+g7f7a36478, py-1.11.0, pluggy-0.13.1
rootdir: /tmp/pytest-of-root/pytest-12/test_xfail_evalfalse_but_fails0
collected 0 items

============================ no tests ran in 0.00s =============================
___________________ TestXFail.test_xfail_not_report_default ____________________
----------------------------- Captured stdout call -----------------------------
============================= test session starts ==============================
platform linux -- Python 3.9.19, pytest-6.0.0rc2.dev33+g7f7a36478, py-1.11.0, pluggy-0.13.1 -- /opt/miniconda3/envs/testbed/bin/python
cachedir: .pytest_cache
rootdir: /tmp/pytest-of-root/pytest-12/test_xfail_not_report_default0
collecting ... collected 1 item

test_one.py::test_this XFAIL                                             [100%]

============================== 1 xfailed in 0.00s ==============================
_________________ TestXFail.test_xfail_not_run_xfail_reporting _________________
----------------------------- Captured stdout call -----------------------------
============================= test session starts ==============================
platform linux -- Python 3.9.19, pytest-6.0.0rc2.dev33+g7f7a36478, py-1.11.0, pluggy-0.13.1
rootdir: /tmp/pytest-of-root/pytest-12/test_xfail_not_run_xfail_reporting0
collected 3 items

test_one.py xx.                                                          [100%]

=========================== short test summary info ============================
XFAIL test_one.py::test_this
  reason: [NOTRUN] noway
XFAIL test_one.py::test_this_true
  reason: [NOTRUN] condition: True
========================= 1 passed, 2 xfailed in 0.07s =========================
__________________ TestXFail.test_xfail_not_run_no_setup_run ___________________
----------------------------- Captured stdout call -----------------------------
============================= test session starts ==============================
platform linux -- Python 3.9.19, pytest-6.0.0rc2.dev33+g7f7a36478, py-1.11.0, pluggy-0.13.1
rootdir: /tmp/pytest-of-root/pytest-12/test_xfail_not_run_no_setup_run0
collected 1 item

test_one.py x                                                            [100%]

=========================== short test summary info ============================
XFAIL test_one.py::test_this
  reason: [NOTRUN] hello
============================== 1 xfailed in 0.03s ==============================
__________________________ TestXFail.test_xfail_xpass __________________________
----------------------------- Captured stdout call -----------------------------
============================= test session starts ==============================
platform linux -- Python 3.9.19, pytest-6.0.0rc2.dev33+g7f7a36478, py-1.11.0, pluggy-0.13.1
rootdir: /tmp/pytest-of-root/pytest-12/test_xfail_xpass0
collected 1 item

test_one.py X                                                            [100%]

=========================== short test summary info ============================
XPASS test_one.py::test_that
============================== 1 xpassed in 0.00s ==============================
_______________________ TestXFail.test_xfail_imperative ________________________
----------------------------- Captured stdout call -----------------------------
============================= test session starts ==============================
platform linux -- Python 3.9.19, pytest-6.0.0rc2.dev33+g7f7a36478, py-1.11.0, pluggy-0.13.1
rootdir: /tmp/pytest-of-root/pytest-12/test_xfail_imperative0
collected 1 item

test_xfail_imperative.py x                                               [100%]

============================== 1 xfailed in 0.01s ==============================
============================= test session starts ==============================
platform linux -- Python 3.9.19, pytest-6.0.0rc2.dev33+g7f7a36478, py-1.11.0, pluggy-0.13.1
rootdir: /tmp/pytest-of-root/pytest-12/test_xfail_imperative0
collected 1 item

test_xfail_imperative.py x                                               [100%]

=========================== short test summary info ============================
XFAIL test_xfail_imperative.py::test_this
  reason: hello
============================== 1 xfailed in 0.00s ==============================
============================= test session starts ==============================
platform linux -- Python 3.9.19, pytest-6.0.0rc2.dev33+g7f7a36478, py-1.11.0, pluggy-0.13.1
rootdir: /tmp/pytest-of-root/pytest-12/test_xfail_imperative0
collected 1 item

test_xfail_imperative.py .                                               [100%]

============================== 1 passed in 0.00s ===============================
______________ TestXFail.test_xfail_imperative_in_setup_function _______________
----------------------------- Captured stdout call -----------------------------
============================= test session starts ==============================
platform linux -- Python 3.9.19, pytest-6.0.0rc2.dev33+g7f7a36478, py-1.11.0, pluggy-0.13.1
rootdir: /tmp/pytest-of-root/pytest-12/test_xfail_imperative_in_setup_function0
collected 1 item

test_xfail_imperative_in_setup_function.py x                             [100%]

============================== 1 xfailed in 0.01s ==============================
============================= test session starts ==============================
platform linux -- Python 3.9.19, pytest-6.0.0rc2.dev33+g7f7a36478, py-1.11.0, pluggy-0.13.1
rootdir: /tmp/pytest-of-root/pytest-12/test_xfail_imperative_in_setup_function0
collected 1 item

test_xfail_imperative_in_setup_function.py x                             [100%]

=========================== short test summary info ============================
XFAIL test_xfail_imperative_in_setup_function.py::test_this
  reason: hello
============================== 1 xfailed in 0.01s ==============================
============================= test session starts ==============================
platform linux -- Python 3.9.19, pytest-6.0.0rc2.dev33+g7f7a36478, py-1.11.0, pluggy-0.13.1
rootdir: /tmp/pytest-of-root/pytest-12/test_xfail_imperative_in_setup_function0
collected 1 item

test_xfail_imperative_in_setup_function.py F                             [100%]

=================================== FAILURES ===================================
__________________________________ test_this ___________________________________

    def test_this():
>       assert 0
E       assert 0

test_xfail_imperative_in_setup_function.py:6: AssertionError
=========================== short test summary info ============================
FAILED test_xfail_imperative_in_setup_function.py::test_this - assert 0
============================== 1 failed in 0.01s ===============================
_____________________ TestXFail.test_dynamic_xfail_no_run ______________________
----------------------------- Captured stdout call -----------------------------
============================= test session starts ==============================
platform linux -- Python 3.9.19, pytest-6.0.0rc2.dev33+g7f7a36478, py-1.11.0, pluggy-0.13.1
rootdir: /tmp/pytest-of-root/pytest-12/test_dynamic_xfail_no_run0
collected 1 item

test_dynamic_xfail_no_run.py x                                           [100%]

=========================== short test summary info ============================
XFAIL test_dynamic_xfail_no_run.py::test_this
  reason: [NOTRUN]
============================== 1 xfailed in 0.04s ==============================
____________ TestXFail.test_dynamic_xfail_set_during_funcarg_setup _____________
----------------------------- Captured stdout call -----------------------------
============================= test session starts ==============================
platform linux -- Python 3.9.19, pytest-6.0.0rc2.dev33+g7f7a36478, py-1.11.0, pluggy-0.13.1
rootdir: /tmp/pytest-of-root/pytest-12/test_dynamic_xfail_set_during_funcarg_setup0
collected 1 item

test_dynamic_xfail_set_during_funcarg_setup.py x                         [100%]

============================== 1 xfailed in 0.01s ==============================
____________ TestXFail.test_dynamic_xfail_set_during_runtest_failed ____________
----------------------------- Captured stdout call -----------------------------
============================= test session starts ==============================
platform linux -- Python 3.9.19, pytest-6.0.0rc2.dev33+g7f7a36478, py-1.11.0, pluggy-0.13.1
rootdir: /tmp/pytest-of-root/pytest-12/test_dynamic_xfail_set_during_runtest_failed0
collected 1 item

test_dynamic_xfail_set_during_runtest_failed.py x                        [100%]

============================== 1 xfailed in 0.01s ==============================
________ TestXFail.test_dynamic_xfail_set_during_runtest_passed_strict _________
----------------------------- Captured stdout call -----------------------------
============================= test session starts ==============================
platform linux -- Python 3.9.19, pytest-6.0.0rc2.dev33+g7f7a36478, py-1.11.0, pluggy-0.13.1
rootdir: /tmp/pytest-of-root/pytest-12/test_dynamic_xfail_set_during_runtest_passed_strict0
collected 1 item

test_dynamic_xfail_set_during_runtest_passed_strict.py F                 [100%]

=================================== FAILURES ===================================
__________________________________ test_this ___________________________________
[XPASS(strict)] xfail
=========================== short test summary info ============================
FAILED test_dynamic_xfail_set_during_runtest_passed_strict.py::test_this
============================== 1 failed in 0.00s ===============================
_________ TestXFail.test_xfail_raises[TypeError-TypeError-*1 xfailed*] _________
----------------------------- Captured stdout call -----------------------------
============================= test session starts ==============================
platform linux -- Python 3.9.19, pytest-6.0.0rc2.dev33+g7f7a36478, py-1.11.0, pluggy-0.13.1
rootdir: /tmp/pytest-of-root/pytest-12/test_xfail_raises0
collected 1 item

test_xfail_raises.py x                                                   [100%]

============================== 1 xfailed in 0.01s ==============================
_ TestXFail.test_xfail_raises[(AttributeError, TypeError)-TypeError-*1 xfailed*] _
----------------------------- Captured stdout call -----------------------------
============================= test session starts ==============================
platform linux -- Python 3.9.19, pytest-6.0.0rc2.dev33+g7f7a36478, py-1.11.0, pluggy-0.13.1
rootdir: /tmp/pytest-of-root/pytest-12/test_xfail_raises1
collected 1 item

test_xfail_raises.py x                                                   [100%]

============================== 1 xfailed in 0.01s ==============================
_________ TestXFail.test_xfail_raises[TypeError-IndexError-*1 failed*] _________
----------------------------- Captured stdout call -----------------------------
============================= test session starts ==============================
platform linux -- Python 3.9.19, pytest-6.0.0rc2.dev33+g7f7a36478, py-1.11.0, pluggy-0.13.1
rootdir: /tmp/pytest-of-root/pytest-12/test_xfail_raises2
collected 1 item

test_xfail_raises.py F                                                   [100%]

=================================== FAILURES ===================================
_________________________________ test_raises __________________________________

    @pytest.mark.xfail(raises=TypeError)
    def test_raises():
>       raise IndexError()
E       IndexError

test_xfail_raises.py:4: IndexError
=========================== short test summary info ============================
FAILED test_xfail_raises.py::test_raises - IndexError
============================== 1 failed in 0.01s ===============================
_ TestXFail.test_xfail_raises[(AttributeError, TypeError)-IndexError-*1 failed*] _
----------------------------- Captured stdout call -----------------------------
============================= test session starts ==============================
platform linux -- Python 3.9.19, pytest-6.0.0rc2.dev33+g7f7a36478, py-1.11.0, pluggy-0.13.1
rootdir: /tmp/pytest-of-root/pytest-12/test_xfail_raises3
collected 1 item

test_xfail_raises.py F                                                   [100%]

=================================== FAILURES ===================================
_________________________________ test_raises __________________________________

    @pytest.mark.xfail(raises=(AttributeError, TypeError))
    def test_raises():
>       raise IndexError()
E       IndexError

test_xfail_raises.py:4: IndexError
=========================== short test summary info ============================
FAILED test_xfail_raises.py::test_raises - IndexError
============================== 1 failed in 0.01s ===============================
_________________________ TestXFail.test_strict_sanity _________________________
----------------------------- Captured stdout call -----------------------------
============================= test session starts ==============================
platform linux -- Python 3.9.19, pytest-6.0.0rc2.dev33+g7f7a36478, py-1.11.0, pluggy-0.13.1
rootdir: /tmp/pytest-of-root/pytest-12/test_strict_sanity0
collected 1 item

test_strict_sanity.py x                                                  [100%]

=========================== short test summary info ============================
XFAIL test_strict_sanity.py::test_foo
  unsupported feature
============================== 1 xfailed in 0.01s ==============================
______________________ TestXFail.test_strict_xfail[True] _______________________
----------------------------- Captured stdout call -----------------------------
============================= test session starts ==============================
platform linux -- Python 3.9.19, pytest-6.0.0rc2.dev33+g7f7a36478, py-1.11.0, pluggy-0.13.1
rootdir: /tmp/pytest-of-root/pytest-12/test_strict_xfail0
collected 1 item

test_strict_xfail.py F                                                   [100%]

=================================== FAILURES ===================================
___________________________________ test_foo ___________________________________
[XPASS(strict)] unsupported feature
============================== 1 failed in 0.00s ===============================
______________________ TestXFail.test_strict_xfail[False] ______________________
----------------------------- Captured stdout call -----------------------------
============================= test session starts ==============================
platform linux -- Python 3.9.19, pytest-6.0.0rc2.dev33+g7f7a36478, py-1.11.0, pluggy-0.13.1
rootdir: /tmp/pytest-of-root/pytest-12/test_strict_xfail1
collected 1 item

test_strict_xfail.py X                                                   [100%]

=========================== short test summary info ============================
XPASS test_strict_xfail.py::test_foo unsupported feature
============================== 1 xpassed in 0.00s ==============================
_________________ TestXFail.test_strict_xfail_condition[True] __________________
----------------------------- Captured stdout call -----------------------------
============================= test session starts ==============================
platform linux -- Python 3.9.19, pytest-6.0.0rc2.dev33+g7f7a36478, py-1.11.0, pluggy-0.13.1
rootdir: /tmp/pytest-of-root/pytest-12/test_strict_xfail_condition0
collected 1 item

test_strict_xfail_condition.py .                                         [100%]

============================== 1 passed in 0.00s ===============================
_________________ TestXFail.test_strict_xfail_condition[False] _________________
----------------------------- Captured stdout call -----------------------------
============================= test session starts ==============================
platform linux -- Python 3.9.19, pytest-6.0.0rc2.dev33+g7f7a36478, py-1.11.0, pluggy-0.13.1
rootdir: /tmp/pytest-of-root/pytest-12/test_strict_xfail_condition1
collected 1 item

test_strict_xfail_condition.py .                                         [100%]

============================== 1 passed in 0.00s ===============================
_________________ TestXFail.test_xfail_condition_keyword[True] _________________
----------------------------- Captured stdout call -----------------------------
============================= test session starts ==============================
platform linux -- Python 3.9.19, pytest-6.0.0rc2.dev33+g7f7a36478, py-1.11.0, pluggy-0.13.1
rootdir: /tmp/pytest-of-root/pytest-12/test_xfail_condition_keyword0
collected 1 item

test_xfail_condition_keyword.py .                                        [100%]

============================== 1 passed in 0.00s ===============================
________________ TestXFail.test_xfail_condition_keyword[False] _________________
----------------------------- Captured stdout call -----------------------------
============================= test session starts ==============================
platform linux -- Python 3.9.19, pytest-6.0.0rc2.dev33+g7f7a36478, py-1.11.0, pluggy-0.13.1
rootdir: /tmp/pytest-of-root/pytest-12/test_xfail_condition_keyword1
collected 1 item

test_xfail_condition_keyword.py .                                        [100%]

============================== 1 passed in 0.00s ===============================
_____________ TestXFail.test_strict_xfail_default_from_file[true] ______________
----------------------------- Captured stdout call -----------------------------
============================= test session starts ==============================
platform linux -- Python 3.9.19, pytest-6.0.0rc2.dev33+g7f7a36478, py-1.11.0, pluggy-0.13.1
rootdir: /tmp/pytest-of-root/pytest-12/test_strict_xfail_default_from_file0, configfile: tox.ini
collected 1 item

test_strict_xfail_default_from_file.py F                                 [100%]

=================================== FAILURES ===================================
___________________________________ test_foo ___________________________________
[XPASS(strict)] unsupported feature
============================== 1 failed in 0.00s ===============================
_____________ TestXFail.test_strict_xfail_default_from_file[false] _____________
----------------------------- Captured stdout call -----------------------------
============================= test session starts ==============================
platform linux -- Python 3.9.19, pytest-6.0.0rc2.dev33+g7f7a36478, py-1.11.0, pluggy-0.13.1
rootdir: /tmp/pytest-of-root/pytest-12/test_strict_xfail_default_from_file1, configfile: tox.ini
collected 1 item

test_strict_xfail_default_from_file.py X                                 [100%]

=========================== short test summary info ============================
XPASS test_strict_xfail_default_from_file.py::test_foo unsupported feature
============================== 1 xpassed in 0.01s ==============================
_____________ TestXFailwithSetupTeardown.test_failing_setup_issue9 _____________
----------------------------- Captured stdout call -----------------------------
============================= test session starts ==============================
platform linux -- Python 3.9.19, pytest-6.0.0rc2.dev33+g7f7a36478, py-1.11.0, pluggy-0.13.1
rootdir: /tmp/pytest-of-root/pytest-12/test_failing_setup_issue90
collected 1 item

test_failing_setup_issue9.py x                                           [100%]

============================== 1 xfailed in 0.01s ==============================
___________ TestXFailwithSetupTeardown.test_failing_teardown_issue9 ____________
----------------------------- Captured stdout call -----------------------------
============================= test session starts ==============================
platform linux -- Python 3.9.19, pytest-6.0.0rc2.dev33+g7f7a36478, py-1.11.0, pluggy-0.13.1
rootdir: /tmp/pytest-of-root/pytest-12/test_failing_teardown_issue90
collected 1 item

test_failing_teardown_issue9.py Xx                                       [100%]

======================== 1 xfailed, 1 xpassed in 0.01s =========================
___________________________ TestSkip.test_skip_class ___________________________
----------------------------- Captured stdout call -----------------------------
============================= test session starts ==============================
platform linux -- Python 3.9.19, pytest-6.0.0rc2.dev33+g7f7a36478, py-1.11.0, pluggy-0.13.1
rootdir: /tmp/pytest-of-root/pytest-12/test_skip_class0
collected 3 items

test_skip_class.py ss.                                                   [100%]

========================= 1 passed, 2 skipped in 0.01s =========================
_____________________ TestSkip.test_skips_on_false_string ______________________
----------------------------- Captured stdout call -----------------------------
============================= test session starts ==============================
platform linux -- Python 3.9.19, pytest-6.0.0rc2.dev33+g7f7a36478, py-1.11.0, pluggy-0.13.1
rootdir: /tmp/pytest-of-root/pytest-12/test_skips_on_false_string0
collected 1 item

test_skips_on_false_string.py s                                          [100%]

============================== 1 skipped in 0.00s ==============================
_________________________ TestSkip.test_arg_as_reason __________________________
----------------------------- Captured stdout call -----------------------------
============================= test session starts ==============================
platform linux -- Python 3.9.19, pytest-6.0.0rc2.dev33+g7f7a36478, py-1.11.0, pluggy-0.13.1
rootdir: /tmp/pytest-of-root/pytest-12/test_arg_as_reason0
collected 1 item

test_arg_as_reason.py s                                                  [100%]

=========================== short test summary info ============================
SKIPPED [1] test_arg_as_reason.py:2: testing stuff
============================== 1 skipped in 0.00s ==============================
_________________________ TestSkip.test_skip_no_reason _________________________
----------------------------- Captured stdout call -----------------------------
============================= test session starts ==============================
platform linux -- Python 3.9.19, pytest-6.0.0rc2.dev33+g7f7a36478, py-1.11.0, pluggy-0.13.1
rootdir: /tmp/pytest-of-root/pytest-12/test_skip_no_reason0
collected 1 item

test_skip_no_reason.py s                                                 [100%]

=========================== short test summary info ============================
SKIPPED [1] test_skip_no_reason.py:2: unconditional skip
============================== 1 skipped in 0.00s ==============================
________________________ TestSkip.test_skip_with_reason ________________________
----------------------------- Captured stdout call -----------------------------
============================= test session starts ==============================
platform linux -- Python 3.9.19, pytest-6.0.0rc2.dev33+g7f7a36478, py-1.11.0, pluggy-0.13.1
rootdir: /tmp/pytest-of-root/pytest-12/test_skip_with_reason0
collected 1 item

test_skip_with_reason.py s                                               [100%]

=========================== short test summary info ============================
SKIPPED [1] test_skip_with_reason.py:2: for lolz
============================== 1 skipped in 0.00s ==============================
_____________________ TestSkip.test_only_skips_marked_test _____________________
----------------------------- Captured stdout call -----------------------------
============================= test session starts ==============================
platform linux -- Python 3.9.19, pytest-6.0.0rc2.dev33+g7f7a36478, py-1.11.0, pluggy-0.13.1
rootdir: /tmp/pytest-of-root/pytest-12/test_only_skips_marked_test0
collected 3 items

test_only_skips_marked_test.py ss.                                       [100%]

=========================== short test summary info ============================
SKIPPED [1] test_only_skips_marked_test.py:2: unconditional skip
SKIPPED [1] test_only_skips_marked_test.py:5: nothing in particular
========================= 1 passed, 2 skipped in 0.01s =========================
________________________ TestSkip.test_strict_and_skip _________________________
----------------------------- Captured stdout call -----------------------------
============================= test session starts ==============================
platform linux -- Python 3.9.19, pytest-6.0.0rc2.dev33+g7f7a36478, py-1.11.0, pluggy-0.13.1
rootdir: /tmp/pytest-of-root/pytest-12/test_strict_and_skip0
collected 1 item

test_strict_and_skip.py s                                                [100%]

=========================== short test summary info ============================
SKIPPED [1] test_strict_and_skip.py:2: unconditional skip
============================== 1 skipped in 0.00s ==============================
______________________ TestSkipif.test_skipif_conditional ______________________
----------------------------- Captured stdout call -----------------------------
============================= test session starts ==============================
platform linux -- Python 3.9.19, pytest-6.0.0rc2.dev33+g7f7a36478, py-1.11.0, pluggy-0.13.1
rootdir: /tmp/pytest-of-root/pytest-12/test_skipif_conditional0
collected 0 items

============================ no tests ran in 0.00s =============================
_________ TestSkipif.test_skipif_reporting["hasattr(sys, 'platform')"] _________
----------------------------- Captured stdout call -----------------------------
============================= test session starts ==============================
platform linux -- Python 3.9.19, pytest-6.0.0rc2.dev33+g7f7a36478, py-1.11.0, pluggy-0.13.1
rootdir: /tmp/pytest-of-root/pytest-12/test_skipif_reporting0
collected 1 item

test_foo.py s

=========================== short test summary info ============================
SKIPPED [1] test_foo.py:2: condition: hasattr(sys, 'platform')
============================== 1 skipped in 0.00s ==============================
______ TestSkipif.test_skipif_reporting[True, reason="invalid platform"] _______
----------------------------- Captured stdout call -----------------------------
============================= test session starts ==============================
platform linux -- Python 3.9.19, pytest-6.0.0rc2.dev33+g7f7a36478, py-1.11.0, pluggy-0.13.1
rootdir: /tmp/pytest-of-root/pytest-12/test_skipif_reporting1
collected 1 item

test_foo.py s

=========================== short test summary info ============================
SKIPPED [1] test_foo.py:2: invalid platform
============================== 1 skipped in 0.00s ==============================
____________________ TestSkipif.test_skipif_using_platform _____________________
----------------------------- Captured stdout call -----------------------------
============================= test session starts ==============================
platform linux -- Python 3.9.19, pytest-6.0.0rc2.dev33+g7f7a36478, py-1.11.0, pluggy-0.13.1
rootdir: /tmp/pytest-of-root/pytest-12/test_skipif_using_platform0
collected 0 items

============================ no tests ran in 0.00s =============================
________ TestSkipif.test_skipif_reporting_multiple[skipif-SKIP-skipped] ________
----------------------------- Captured stdout call -----------------------------
============================= test session starts ==============================
platform linux -- Python 3.9.19, pytest-6.0.0rc2.dev33+g7f7a36478, py-1.11.0, pluggy-0.13.1
rootdir: /tmp/pytest-of-root/pytest-12/test_skipif_reporting_multiple0
collected 1 item

test_foo.py s

=========================== short test summary info ============================
SKIPPED [1] test_foo.py:2: second_condition
============================== 1 skipped in 0.00s ==============================
________ TestSkipif.test_skipif_reporting_multiple[xfail-XPASS-xpassed] ________
----------------------------- Captured stdout call -----------------------------
============================= test session starts ==============================
platform linux -- Python 3.9.19, pytest-6.0.0rc2.dev33+g7f7a36478, py-1.11.0, pluggy-0.13.1
rootdir: /tmp/pytest-of-root/pytest-12/test_skipif_reporting_multiple1
collected 1 item

test_foo.py X

=========================== short test summary info ============================
XPASS test_foo.py::test_foobar second_condition
============================== 1 xpassed in 0.00s ==============================
_________________________ test_skip_not_report_default _________________________
----------------------------- Captured stdout call -----------------------------
============================= test session starts ==============================
platform linux -- Python 3.9.19, pytest-6.0.0rc2.dev33+g7f7a36478, py-1.11.0, pluggy-0.13.1 -- /opt/miniconda3/envs/testbed/bin/python
cachedir: .pytest_cache
rootdir: /tmp/pytest-of-root/pytest-12/test_skip_not_report_default0
collecting ... collected 1 item

test_one.py::test_this SKIPPED                                           [100%]

============================== 1 skipped in 0.00s ==============================
______________________________ test_skipif_class _______________________________
----------------------------- Captured stdout call -----------------------------
============================= test session starts ==============================
platform linux -- Python 3.9.19, pytest-6.0.0rc2.dev33+g7f7a36478, py-1.11.0, pluggy-0.13.1
rootdir: /tmp/pytest-of-root/pytest-12/test_skipif_class1
collected 2 items

test_skipif_class.py ss                                                  [100%]

============================== 2 skipped in 0.01s ==============================
_______________________ test_skipped_reasons_functional ________________________
----------------------------- Captured stdout call -----------------------------
============================= test session starts ==============================
platform linux -- Python 3.9.19, pytest-6.0.0rc2.dev33+g7f7a36478, py-1.11.0, pluggy-0.13.1
rootdir: /tmp/pytest-of-root/pytest-12/test_skipped_reasons_functional0
collected 3 items

test_one.py sss                                                          [100%]

=========================== short test summary info ============================
SKIPPED [2] conftest.py:4: test
SKIPPED [1] test_one.py:14: via_decorator
============================== 3 skipped in 0.01s ==============================
_____________________________ test_skipped_folding _____________________________
----------------------------- Captured stdout call -----------------------------
============================= test session starts ==============================
platform linux -- Python 3.9.19, pytest-6.0.0rc2.dev33+g7f7a36478, py-1.11.0, pluggy-0.13.1
rootdir: /tmp/pytest-of-root/pytest-12/test_skipped_folding0
collected 2 items

test_one.py ss                                                           [100%]

=========================== short test summary info ============================
SKIPPED [2] test_one.py: Folding
============================== 2 skipped in 0.01s ==============================
_______________________________ test_reportchars _______________________________
----------------------------- Captured stdout call -----------------------------
============================= test session starts ==============================
platform linux -- Python 3.9.19, pytest-6.0.0rc2.dev33+g7f7a36478, py-1.11.0, pluggy-0.13.1
rootdir: /tmp/pytest-of-root/pytest-12/test_reportchars0
collected 4 items

test_reportchars.py FxXs                                                 [100%]

=================================== FAILURES ===================================
____________________________________ test_1 ____________________________________

    def test_1():
>       assert 0
E       assert 0

test_reportchars.py:3: AssertionError
=========================== short test summary info ============================
FAILED test_reportchars.py::test_1 - assert 0
XFAIL test_reportchars.py::test_2
XPASS test_reportchars.py::test_3
SKIPPED [1] test_reportchars.py:11: four
============== 1 failed, 1 skipped, 1 xfailed, 1 xpassed in 0.01s ==============
____________________________ test_reportchars_error ____________________________
----------------------------- Captured stdout call -----------------------------
============================= test session starts ==============================
platform linux -- Python 3.9.19, pytest-6.0.0rc2.dev33+g7f7a36478, py-1.11.0, pluggy-0.13.1
rootdir: /tmp/pytest-of-root/pytest-12/test_reportchars_error0
collected 1 item

test_simple.py .E                                                        [100%]

==================================== ERRORS ====================================
________________________ ERROR at teardown of test_foo _________________________

    def pytest_runtest_teardown():
>       assert 0
E       assert 0

conftest.py:2: AssertionError
=========================== short test summary info ============================
ERROR test_simple.py::test_foo - assert 0
========================== 1 passed, 1 error in 0.01s ==========================
_____________________________ test_reportchars_all _____________________________
----------------------------- Captured stdout call -----------------------------
============================= test session starts ==============================
platform linux -- Python 3.9.19, pytest-6.0.0rc2.dev33+g7f7a36478, py-1.11.0, pluggy-0.13.1
rootdir: /tmp/pytest-of-root/pytest-12/test_reportchars_all0
collected 5 items

test_reportchars_all.py FxXsE                                            [100%]

==================================== ERRORS ====================================
___________________________ ERROR at setup of test_5 ___________________________

    @pytest.fixture
    def fail():
>       assert 0
E       assert 0

test_reportchars_all.py:14: AssertionError
=================================== FAILURES ===================================
____________________________________ test_1 ____________________________________

    def test_1():
>       assert 0
E       assert 0

test_reportchars_all.py:3: AssertionError
=========================== short test summary info ============================
SKIPPED [1] test_reportchars_all.py:11: four
XFAIL test_reportchars_all.py::test_2
XPASS test_reportchars_all.py::test_3
ERROR test_reportchars_all.py::test_5 - assert 0
FAILED test_reportchars_all.py::test_1 - assert 0
========= 1 failed, 1 skipped, 1 xfailed, 1 xpassed, 1 error in 0.01s ==========
__________________________ test_reportchars_all_error __________________________
----------------------------- Captured stdout call -----------------------------
============================= test session starts ==============================
platform linux -- Python 3.9.19, pytest-6.0.0rc2.dev33+g7f7a36478, py-1.11.0, pluggy-0.13.1
rootdir: /tmp/pytest-of-root/pytest-12/test_reportchars_all_error0
collected 1 item

test_simple.py .E                                                        [100%]

==================================== ERRORS ====================================
________________________ ERROR at teardown of test_foo _________________________

    def pytest_runtest_teardown():
>       assert 0
E       assert 0

conftest.py:2: AssertionError
=========================== short test summary info ============================
ERROR test_simple.py::test_foo - assert 0
========================== 1 passed, 1 error in 0.01s ==========================
________________________ test_xfail_skipif_with_globals ________________________
----------------------------- Captured stdout call -----------------------------
============================= test session starts ==============================
platform linux -- Python 3.9.19, pytest-6.0.0rc2.dev33+g7f7a36478, py-1.11.0, pluggy-0.13.1
rootdir: /tmp/pytest-of-root/pytest-12/test_xfail_skipif_with_globals0
collected 2 items

test_xfail_skipif_with_globals.py sx                                     [100%]

=========================== short test summary info ============================
SKIPPED [1] test_xfail_skipif_with_globals.py:3: condition: x == 3
XFAIL test_xfail_skipif_with_globals.py::test_boolean
  condition: x == 3
======================== 1 skipped, 1 xfailed in 0.01s =========================
_____________________________ test_default_markers _____________________________
----------------------------- Captured stdout call -----------------------------
@pytest.mark.filterwarnings(warning): add a warning filter to the given test. see https://docs.pytest.org/en/stable/warnings.html#pytest-mark-filterwarnings

@pytest.mark.skip(reason=None): skip the given test function with an optional reason. Example: skip(reason="no way of currently testing this") skips the test.

@pytest.mark.skipif(condition, ..., *, reason=...): skip the given test function if any of the conditions evaluate to True. Example: skipif(sys.platform == 'win32') skips the test if we are on the win32 platform. See https://docs.pytest.org/en/stable/reference.html#pytest-mark-skipif

@pytest.mark.xfail(condition, ..., *, reason=..., run=True, raises=None, strict=xfail_strict): mark the test function as an expected failure if any of the conditions evaluate to True. Optionally specify a reason for better reporting and run=False if you don't even want to execute the test function. If only specific exception(s) are expected, you can list them in raises, and if the test fails in other ways, it will be reported as a true failure. See https://docs.pytest.org/en/stable/reference.html#pytest-mark-xfail

@pytest.mark.parametrize(argnames, argvalues): call a test function multiple times passing in different arguments in turn. argvalues generally needs to be a list of values if argnames specifies only one name or a list of tuples of values if argnames specifies multiple names. Example: @parametrize('arg1', [1,2]) would lead to two calls of the decorated test function, one with arg1=1 and another with arg1=2.see https://docs.pytest.org/en/stable/parametrize.html for more info and examples.

@pytest.mark.usefixtures(fixturename1, fixturename2, ...): mark tests as needing all of the specified fixtures. see https://docs.pytest.org/en/stable/fixture.html#usefixtures

@pytest.mark.tryfirst: mark a hook implementation function such that the plugin machinery will try to call it first/as early as possible.

@pytest.mark.trylast: mark a hook implementation function such that the plugin machinery will try to call it last/as late as possible.

_______________________ test_xfail_test_setup_exception ________________________
----------------------------- Captured stdout call -----------------------------
============================= test session starts ==============================
platform linux -- Python 3.9.19, pytest-6.0.0rc2.dev33+g7f7a36478, py-1.11.0, pluggy-0.13.1
rootdir: /tmp/pytest-of-root/pytest-12/test_xfail_test_setup_exception0
collected 1 item

test_xfail_test_setup_exception.py x                                     [100%]

============================== 1 xfailed in 0.01s ==============================
______________________ test_imperativeskip_on_xfail_test _______________________
----------------------------- Captured stdout call -----------------------------
============================= test session starts ==============================
platform linux -- Python 3.9.19, pytest-6.0.0rc2.dev33+g7f7a36478, py-1.11.0, pluggy-0.13.1
rootdir: /tmp/pytest-of-root/pytest-12/test_imperativeskip_on_xfail_test0
collected 2 items

test_imperativeskip_on_xfail_test.py ss                                  [100%]

=========================== short test summary info ============================
SKIPPED [1] conftest.py:3: abc
SKIPPED [1] test_imperativeskip_on_xfail_test.py:6: condition: True
============================== 2 skipped in 0.01s ==============================
_______________________ TestBooleanCondition.test_skipif _______________________
----------------------------- Captured stdout call -----------------------------
============================= test session starts ==============================
platform linux -- Python 3.9.19, pytest-6.0.0rc2.dev33+g7f7a36478, py-1.11.0, pluggy-0.13.1
rootdir: /tmp/pytest-of-root/pytest-12/test_skipif0
collected 2 items

test_skipif.py s.                                                        [100%]

========================= 1 passed, 1 skipped in 0.01s =========================
__________________ TestBooleanCondition.test_skipif_noreason ___________________
----------------------------- Captured stdout call -----------------------------
============================= test session starts ==============================
platform linux -- Python 3.9.19, pytest-6.0.0rc2.dev33+g7f7a36478, py-1.11.0, pluggy-0.13.1
rootdir: /tmp/pytest-of-root/pytest-12/test_skipif_noreason0
collected 1 item

test_skipif_noreason.py E                                                [100%]

==================================== ERRORS ====================================
_________________________ ERROR at setup of test_func __________________________
Error evaluating 'skipif': you need to specify reason=STRING when using booleans as conditions.
=============================== 1 error in 0.01s ===============================
_______________________ TestBooleanCondition.test_xfail ________________________
----------------------------- Captured stdout call -----------------------------
============================= test session starts ==============================
platform linux -- Python 3.9.19, pytest-6.0.0rc2.dev33+g7f7a36478, py-1.11.0, pluggy-0.13.1
rootdir: /tmp/pytest-of-root/pytest-12/test_xfail0
collected 1 item

test_xfail.py x                                                          [100%]

=========================== short test summary info ============================
XFAIL test_xfail.py::test_func
  True123
============================== 1 xfailed in 0.01s ==============================
_______________________________ test_xfail_item ________________________________
----------------------------- Captured stdout call -----------------------------
============================= test session starts ==============================
platform linux -- Python 3.9.19, pytest-6.0.0rc2.dev33+g7f7a36478, py-1.11.0, pluggy-0.13.1
rootdir: /tmp/pytest-of-root/pytest-12/test_xfail_item0
collected 1 item

foo x                                                                    [100%]

=============================== warnings summary ===============================
conftest.py:9
  /tmp/pytest-of-root/pytest-12/test_xfail_item0/conftest.py:9: PytestDeprecationWarning: Direct construction of MyItem has been deprecated, please use MyItem.from_parent.
  See https://docs.pytest.org/en/stable/deprecations.html#node-construction-changed-to-node-from-parent for more details.
    return MyItem("foo", parent)

-- Docs: https://docs.pytest.org/en/stable/warnings.html
======================== 1 xfailed, 1 warning in 0.05s =========================
_________________________ test_module_level_skip_error _________________________
----------------------------- Captured stdout call -----------------------------
============================= test session starts ==============================
platform linux -- Python 3.9.19, pytest-6.0.0rc2.dev33+g7f7a36478, py-1.11.0, pluggy-0.13.1
rootdir: /tmp/pytest-of-root/pytest-12/test_module_level_skip_error0
collected 0 items / 1 error

==================================== ERRORS ====================================
_______________ ERROR collecting test_module_level_skip_error.py _______________
Using pytest.skip outside of a test is not allowed. To decorate a test function, use the @pytest.mark.skip or @pytest.mark.skipif decorators instead, and to skip a module use `pytestmark = pytest.mark.{skip,skipif}.
=========================== short test summary info ============================
ERROR test_module_level_skip_error.py
!!!!!!!!!!!!!!!!!!!! Interrupted: 1 error during collection !!!!!!!!!!!!!!!!!!!!
=============================== 1 error in 0.05s ===============================
________________ test_module_level_skip_with_allow_module_level ________________
----------------------------- Captured stdout call -----------------------------
============================= test session starts ==============================
platform linux -- Python 3.9.19, pytest-6.0.0rc2.dev33+g7f7a36478, py-1.11.0, pluggy-0.13.1
rootdir: /tmp/pytest-of-root/pytest-12/test_module_level_skip_with_allow_module_level0
collected 0 items / 1 skipped

=========================== short test summary info ============================
SKIPPED [1] test_module_level_skip_with_allow_module_level.py:2: skip_module_level
============================== 1 skipped in 0.00s ==============================
_____________________ test_invalid_skip_keyword_parameter ______________________
----------------------------- Captured stdout call -----------------------------
============================= test session starts ==============================
platform linux -- Python 3.9.19, pytest-6.0.0rc2.dev33+g7f7a36478, py-1.11.0, pluggy-0.13.1
rootdir: /tmp/pytest-of-root/pytest-12/test_invalid_skip_keyword_parameter0
collected 0 items / 1 error

==================================== ERRORS ====================================
___________ ERROR collecting test_invalid_skip_keyword_parameter.py ____________
test_invalid_skip_keyword_parameter.py:2: in <module>
    pytest.skip("skip_module_level", unknown=1)
E   TypeError: skip() got an unexpected keyword argument 'unknown'
=========================== short test summary info ============================
ERROR test_invalid_skip_keyword_parameter.py - TypeError: skip() got an unexp...
!!!!!!!!!!!!!!!!!!!! Interrupted: 1 error during collection !!!!!!!!!!!!!!!!!!!!
=============================== 1 error in 0.04s ===============================
_____________________________ test_mark_xfail_item _____________________________
----------------------------- Captured stdout call -----------------------------
============================= test session starts ==============================
platform linux -- Python 3.9.19, pytest-6.0.0rc2.dev33+g7f7a36478, py-1.11.0, pluggy-0.13.1
rootdir: /tmp/pytest-of-root/pytest-12/test_mark_xfail_item0
collected 1 item

foo x                                                                    [100%]

=============================== warnings summary ===============================
conftest.py:14
  /tmp/pytest-of-root/pytest-12/test_mark_xfail_item0/conftest.py:14: PytestDeprecationWarning: Direct construction of MyItem has been deprecated, please use MyItem.from_parent.
  See https://docs.pytest.org/en/stable/deprecations.html#node-construction-changed-to-node-from-parent for more details.
    return MyItem("foo", parent)

-- Docs: https://docs.pytest.org/en/stable/warnings.html
======================== 1 xfailed, 1 warning in 0.03s =========================
________________________ test_summary_list_after_errors ________________________
----------------------------- Captured stdout call -----------------------------
============================= test session starts ==============================
platform linux -- Python 3.9.19, pytest-6.0.0rc2.dev33+g7f7a36478, py-1.11.0, pluggy-0.13.1
rootdir: /tmp/pytest-of-root/pytest-12/test_summary_list_after_errors0
collected 1 item

test_summary_list_after_errors.py F                                      [100%]

=================================== FAILURES ===================================
__________________________________ test_fail ___________________________________

    def test_fail():
>       assert 0
E       assert 0

test_summary_list_after_errors.py:3: AssertionError
=========================== short test summary info ============================
FAILED test_summary_list_after_errors.py::test_fail - assert 0
============================== 1 failed in 0.01s ===============================
_____________________________ test_relpath_rootdir _____________________________
----------------------------- Captured stdout call -----------------------------
============================= test session starts ==============================
platform linux -- Python 3.9.19, pytest-6.0.0rc2.dev33+g7f7a36478, py-1.11.0, pluggy-0.13.1
rootdir: /tmp/pytest-of-root/pytest-12/test_relpath_rootdir0/tests
collected 1 item

tests/test_1.py s                                                        [100%]

=========================== short test summary info ============================
SKIPPED [1] tests/test_1.py:2: unconditional skip
============================== 1 skipped in 0.00s ==============================
=========================== short test summary info ============================
PASSED testing/test_skipping.py::test_importorskip
PASSED testing/test_skipping.py::TestEvaluation::test_no_marker
PASSED testing/test_skipping.py::TestEvaluation::test_marked_xfail_no_args
PASSED testing/test_skipping.py::TestEvaluation::test_marked_skipif_no_args
PASSED testing/test_skipping.py::TestEvaluation::test_marked_one_arg
PASSED testing/test_skipping.py::TestEvaluation::test_marked_one_arg_with_reason
PASSED testing/test_skipping.py::TestEvaluation::test_marked_one_arg_twice
PASSED testing/test_skipping.py::TestEvaluation::test_marked_one_arg_twice2
PASSED testing/test_skipping.py::TestEvaluation::test_marked_skipif_with_boolean_without_reason
PASSED testing/test_skipping.py::TestEvaluation::test_marked_skipif_with_invalid_boolean
PASSED testing/test_skipping.py::TestEvaluation::test_skipif_class
PASSED testing/test_skipping.py::TestXFail::test_xfail_simple[True]
PASSED testing/test_skipping.py::TestXFail::test_xfail_simple[False]
PASSED testing/test_skipping.py::TestXFail::test_xfail_xpassed
PASSED testing/test_skipping.py::TestXFail::test_xfail_using_platform
PASSED testing/test_skipping.py::TestXFail::test_xfail_xpassed_strict
PASSED testing/test_skipping.py::TestXFail::test_xfail_run_anyway
PASSED testing/test_skipping.py::TestXFail::test_xfail_run_with_skip_mark[test_input0-expected0]
PASSED testing/test_skipping.py::TestXFail::test_xfail_run_with_skip_mark[test_input1-expected1]
PASSED testing/test_skipping.py::TestXFail::test_xfail_evalfalse_but_fails
PASSED testing/test_skipping.py::TestXFail::test_xfail_not_report_default
PASSED testing/test_skipping.py::TestXFail::test_xfail_not_run_xfail_reporting
PASSED testing/test_skipping.py::TestXFail::test_xfail_not_run_no_setup_run
PASSED testing/test_skipping.py::TestXFail::test_xfail_xpass
PASSED testing/test_skipping.py::TestXFail::test_xfail_imperative
PASSED testing/test_skipping.py::TestXFail::test_xfail_imperative_in_setup_function
PASSED testing/test_skipping.py::TestXFail::test_dynamic_xfail_no_run
PASSED testing/test_skipping.py::TestXFail::test_dynamic_xfail_set_during_funcarg_setup
PASSED testing/test_skipping.py::TestXFail::test_dynamic_xfail_set_during_runtest_failed
PASSED testing/test_skipping.py::TestXFail::test_dynamic_xfail_set_during_runtest_passed_strict
PASSED testing/test_skipping.py::TestXFail::test_xfail_raises[TypeError-TypeError-*1 xfailed*]
PASSED testing/test_skipping.py::TestXFail::test_xfail_raises[(AttributeError, TypeError)-TypeError-*1 xfailed*]
PASSED testing/test_skipping.py::TestXFail::test_xfail_raises[TypeError-IndexError-*1 failed*]
PASSED testing/test_skipping.py::TestXFail::test_xfail_raises[(AttributeError, TypeError)-IndexError-*1 failed*]
PASSED testing/test_skipping.py::TestXFail::test_strict_sanity
PASSED testing/test_skipping.py::TestXFail::test_strict_xfail[True]
PASSED testing/test_skipping.py::TestXFail::test_strict_xfail[False]
PASSED testing/test_skipping.py::TestXFail::test_strict_xfail_condition[True]
PASSED testing/test_skipping.py::TestXFail::test_strict_xfail_condition[False]
PASSED testing/test_skipping.py::TestXFail::test_xfail_condition_keyword[True]
PASSED testing/test_skipping.py::TestXFail::test_xfail_condition_keyword[False]
PASSED testing/test_skipping.py::TestXFail::test_strict_xfail_default_from_file[true]
PASSED testing/test_skipping.py::TestXFail::test_strict_xfail_default_from_file[false]
PASSED testing/test_skipping.py::TestXFailwithSetupTeardown::test_failing_setup_issue9
PASSED testing/test_skipping.py::TestXFailwithSetupTeardown::test_failing_teardown_issue9
PASSED testing/test_skipping.py::TestSkip::test_skip_class
PASSED testing/test_skipping.py::TestSkip::test_skips_on_false_string
PASSED testing/test_skipping.py::TestSkip::test_arg_as_reason
PASSED testing/test_skipping.py::TestSkip::test_skip_no_reason
PASSED testing/test_skipping.py::TestSkip::test_skip_with_reason
PASSED testing/test_skipping.py::TestSkip::test_only_skips_marked_test
PASSED testing/test_skipping.py::TestSkip::test_strict_and_skip
PASSED testing/test_skipping.py::TestSkipif::test_skipif_conditional
PASSED testing/test_skipping.py::TestSkipif::test_skipif_reporting["hasattr(sys, 'platform')"]
PASSED testing/test_skipping.py::TestSkipif::test_skipif_reporting[True, reason="invalid platform"]
PASSED testing/test_skipping.py::TestSkipif::test_skipif_using_platform
PASSED testing/test_skipping.py::TestSkipif::test_skipif_reporting_multiple[skipif-SKIP-skipped]
PASSED testing/test_skipping.py::TestSkipif::test_skipif_reporting_multiple[xfail-XPASS-xpassed]
PASSED testing/test_skipping.py::test_skip_not_report_default
PASSED testing/test_skipping.py::test_skipif_class
PASSED testing/test_skipping.py::test_skipped_reasons_functional
PASSED testing/test_skipping.py::test_skipped_folding
PASSED testing/test_skipping.py::test_reportchars
PASSED testing/test_skipping.py::test_reportchars_error
PASSED testing/test_skipping.py::test_reportchars_all
PASSED testing/test_skipping.py::test_reportchars_all_error
PASSED testing/test_skipping.py::test_xfail_skipif_with_globals
PASSED testing/test_skipping.py::test_default_markers
PASSED testing/test_skipping.py::test_xfail_test_setup_exception
PASSED testing/test_skipping.py::test_imperativeskip_on_xfail_test
PASSED testing/test_skipping.py::TestBooleanCondition::test_skipif
PASSED testing/test_skipping.py::TestBooleanCondition::test_skipif_noreason
PASSED testing/test_skipping.py::TestBooleanCondition::test_xfail
PASSED testing/test_skipping.py::test_xfail_item
PASSED testing/test_skipping.py::test_module_level_skip_error
PASSED testing/test_skipping.py::test_module_level_skip_with_allow_module_level
PASSED testing/test_skipping.py::test_invalid_skip_keyword_parameter
PASSED testing/test_skipping.py::test_mark_xfail_item
PASSED testing/test_skipping.py::test_summary_list_after_errors
PASSED testing/test_skipping.py::test_relpath_rootdir
FAILED testing/test_skipping.py::test_errors_in_xfail_skip_expressions - Fail...
========================= 1 failed, 80 passed in 2.53s =========================
Updated 1 path from 6966cf7b5

