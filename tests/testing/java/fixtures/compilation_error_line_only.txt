[INFO] Scanning for projects...
[INFO]
[INFO] --------------------------< com.example:app >--------------------------
[INFO] Building app 0.0.1-SNAPSHOT
[INFO]   from pom.xml
[INFO] --------------------------------[ jar ]---------------------------------
[INFO]
[INFO] --- jacoco:0.8.11:prepare-agent (prepare-agent) @ app ---
[INFO] argLine set to -javaagent:/Users/<USER>/.m2/repository/org/jacoco/org.jacoco.agent/0.8.11/org.jacoco.agent-0.8.11-runtime.jar=destfile=/Users/<USER>/repos/example/app-api/target/jacoco.exec
[INFO]
[INFO] --- resources:3.3.1:resources (default-resources) @ app ---
[INFO] Copying 1 resource from src/main/resources to target/classes
[INFO] Copying 44 resources from src/main/resources to target/classes
[INFO]
[INFO] --- compiler:3.12.1:compile (default-compile) @ app ---
[INFO] Recompiling the module because of changed source code.
[INFO] Compiling 324 source files with javac [debug parameters release 21] to target/classes
[INFO] /Users/<USER>/repos/example/app-api/src/main/java/com/example/core/domain/Person.java: Some input files use or override a deprecated API.
[INFO] /Users/<USER>/repos/example/app-api/src/main/java/com/example/core/domain/Person.java: Recompile with -Xlint:deprecation for details.
[INFO] /Users/<USER>/repos/example/app-api/src/main/java/com/example/simulation/util/PersonaJsonLoader.java: Some input files use unchecked or unsafe operations.
[INFO] /Users/<USER>/repos/example/app-api/src/main/java/com/example/simulation/util/PersonaJsonLoader.java: Recompile with -Xlint:unchecked for details.
[INFO] -------------------------------------------------------------
[WARNING] COMPILATION WARNING :
[INFO] -------------------------------------------------------------
[WARNING] /Users/<USER>/repos/example/app-api/src/main/java/com/example/transactions/domain/Transaction.java:[69,33] @Builder will ignore the initializing expression entirely. If you want the initializing expression to serve as default, add @Builder.Default. If it is not supposed to be settable during building, make the field final.
[WARNING] /Users/<USER>/repos/example/app-api/src/main/java/com/example/core/domain/BaseEntity.java:[35,23] @SuperBuilder will ignore the initializing expression entirely. If you want the initializing expression to serve as default, add @Builder.Default. If it is not supposed to be settable during building, make the field final.
[WARNING] /Users/<USER>/repos/example/app-api/src/main/java/com/example/core/domain/BeneficialOwner.java:[13,1] Generating equals/hashCode implementation but without a call to superclass, even though this class does not extend java.lang.Object. If this is intentional, add '@EqualsAndHashCode(callSuper=false)' to your type.
[WARNING] /Users/<USER>/repos/example/app-api/src/main/java/com/example/core/domain/Organization.java:[13,1] Generating equals/hashCode implementation but without a call to superclass, even though this class does not extend java.lang.Object. If this is intentional, add '@EqualsAndHashCode(callSuper=false)' to your type.
[WARNING] /Users/<USER>/repos/example/app-api/src/main/java/com/example/core/domain/Address.java:[13,1] Generating equals/hashCode implementation but without a call to superclass, even though this class does not extend java.lang.Object. If this is intentional, add '@EqualsAndHashCode(callSuper=false)' to your type.
[WARNING] /Users/<USER>/repos/example/app-api/src/main/java/com/example/core/domain/Person.java:[16,1] Generating equals/hashCode implementation but without a call to superclass, even though this class does not extend java.lang.Object. If this is intentional, add '@EqualsAndHashCode(callSuper=false)' to your type.
[INFO] 6 warnings
[INFO] -------------------------------------------------------------
[INFO] -------------------------------------------------------------
[ERROR] COMPILATION ERROR :
[INFO] -------------------------------------------------------------
[ERROR] /Users/<USER>/repos/example/app-api/src/main/java/com/example/core/api/BeneficialOwnerController.java:[55,64] cannot infer type arguments for com.example.core.dto.PageResponseDto<>
  reason: cannot infer type-variable(s) T
    (actual and formal argument lists differ in length)
[ERROR] /Users/<USER>/repos/example/app-api/src/main/java/com/example/core/services/impl/BeneficialOwnerServiceImpl.java:[143,79] incompatible types: java.util.List<com.example.core.domain.Address> cannot be converted to java.util.Set<com.example.core.domain.Address>
[ERROR] /Users/<USER>/repos/example/app-api/src/main/java/com/example/core/services/impl/BeneficialOwnerServiceImpl.java:[148,25] cannot find symbol
  symbol:   method id(java.util.UUID)
  location: class com.example.core.dto.AddressDto.AddressDtoBuilder
[ERROR] /Users/<USER>/repos/example/app-api/src/main/java/com/example/core/services/impl/BeneficialOwnerServiceImpl.java:[155,25] incompatible types: inference variable T has incompatible bounds
    equality constraints: com.example.core.dto.AddressDto
    lower bounds: java.lang.Object
[INFO] 4 errors
[INFO] -------------------------------------------------------------
[INFO] ------------------------------------------------------------------------
[INFO] BUILD FAILURE
[INFO] ------------------------------------------------------------------------
[INFO] Total time:  2.550 s
[INFO] Finished at: 2025-04-11T13:40:22+02:00
[INFO] ------------------------------------------------------------------------
[ERROR] Failed to execute goal org.apache.maven.plugins:maven-compiler-plugin:3.12.1:compile (default-compile) on project app: Compilation failure: Compilation failure:
[ERROR] /Users/<USER>/repos/example/app-api/src/main/java/com/example/core/api/BeneficialOwnerController.java:[55,64] cannot infer type arguments for com.example.core.dto.PageResponseDto<>
[ERROR]   reason: cannot infer type-variable(s) T
[ERROR]     (actual and formal argument lists differ in length)
[ERROR] /Users/<USER>/repos/example/app-api/src/main/java/com/example/core/services/impl/BeneficialOwnerServiceImpl.java:[143,79] incompatible types: java.util.List<com.example.core.domain.Address> cannot be converted to java.util.Set<com.example.core.domain.Address>
[ERROR] /Users/<USER>/repos/example/app-api/src/main/java/com/example/core/services/impl/BeneficialOwnerServiceImpl.java:[148,25] cannot find symbol
[ERROR]   symbol:   method id(java.util.UUID)
[ERROR]   location: class com.example.core.dto.AddressDto.AddressDtoBuilder
[ERROR] /Users/<USER>/repos/example/app-api/src/main/java/com/example/core/services/impl/BeneficialOwnerServiceImpl.java:[155,25] incompatible types: inference variable T has incompatible bounds
[ERROR]     equality constraints: com.example.core.dto.AddressDto
[ERROR]     lower bounds: java.lang.Object
[ERROR] -> [Help 1]
[ERROR]
[ERROR] To see the full stack trace of the errors, re-run Maven with the -e switch.
[ERROR] Re-run Maven using the -X switch to enable full debug logging.
[ERROR]
[ERROR] For more information about the errors and possible solutions, please read the following articles:
[ERROR] [Help 1] http://cwiki.apache.org/confluence/display/MAVEN/MojoFailureException
