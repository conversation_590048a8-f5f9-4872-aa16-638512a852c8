[INFO] Scanning for projects...
[INFO] 
[INFO] ------------------------< com.example:my-app >-------------------------
[INFO] Building my-app 1.0-SNAPSHOT
[INFO] --------------------------------[ jar ]---------------------------------
[INFO] 
[INFO] --- maven-surefire-plugin:2.22.1:test (default-test) @ my-app ---
[INFO] 
[INFO] -------------------------------------------------------
[INFO]  T E S T S
[INFO] -------------------------------------------------------
[INFO] Running com.example.SuccessTest
[INFO] Tests run: 3, Failures: 0, Errors: 0, Skipped: 0, Time elapsed: 0.185 s - in com.example.SuccessTest
[INFO] Running com.example.FailTest
[ERROR] Tests run: 3, Failures: 1, Errors: 0, Skipped: 0, Time elapsed: 0.289 s <<< FAILURE! - in com.example.FailTest
[ERROR] com.example.FailTest#testSomething  Time elapsed: 0.121 s  <<< FAILURE!
java.lang.AssertionError: expected:<true> but was:<false>
    at org.junit.Assert.fail(Assert.java:89)
    at org.junit.Assert.failNotEquals(Assert.java:835)
    at org.junit.Assert.assertEquals(Assert.java:120)
    at org.junit.Assert.assertEquals(Assert.java:146)
    at com.example.FailTest.testSomething(FailTest.java:42)
[INFO] Running com.example.ErrorTest
[ERROR] Tests run: 3, Failures: 0, Errors: 1, Skipped: 0, Time elapsed: 0.275 s <<< FAILURE! - in com.example.ErrorTest
[ERROR] com.example.ErrorTest#testWithError  Time elapsed: 0.134 s  <<< ERROR!
java.lang.NullPointerException
    at com.example.ErrorTest.testWithError(ErrorTest.java:58)

[INFO] 
[INFO] Results:
[INFO] 
[ERROR] Failures: 
[ERROR]   com.example.FailTest.testSomething:42 expected:<true> but was:<false>
[ERROR] Errors: 
[ERROR]   com.example.ErrorTest.testWithError:58 » NullPointer
[INFO] 
[ERROR] Tests run: 9, Failures: 1, Errors: 1, Skipped: 0
[INFO] 
[INFO] ------------------------------------------------------------------------
[INFO] BUILD FAILURE
[INFO] ------------------------------------------------------------------------
[INFO] Total time:  4.631 s
[INFO] Finished at: 2023-05-15T14:32:45-07:00
[INFO] ------------------------------------------------------------------------
[ERROR] Failed to execute goal org.apache.maven.plugins:maven-surefire-plugin:2.22.1:test (default-test) on project my-app: There are test failures.
[ERROR] 
[ERROR] Please refer to target/surefire-reports for the individual test results.
[ERROR] Please refer to dump files (if any exist) [date].dump, [date]-jvmRun[N].dump and [date].dumpstream.
[ERROR] -> [Help 1]
