{"cells": [{"cell_type": "code", "id": "611e5d6d74f07ecf", "metadata": {"ExecuteTime": {"end_time": "2024-06-17T18:02:27.444364Z", "start_time": "2024-06-17T18:02:23.511519Z"}}, "source": ["import litellm\n", "import datetime\n", "import os\n", "\n", "index_store_dir = f\"/home/<USER>/20240522-voyage-code-2\"\n", "repo_base_dir = f\"/tmp/repos\"\n", "\n", "model = \"gpt-3.5-turbo-0125\" # \"gpt-4o-2024-05-13\"\n", "\n", "date_str = datetime.datetime.now().strftime(\"%Y%m%d\")\n", "model_file_name = f\"{model.replace('/', '_')}\"\n", "\n", "evaluations_dir = \"/home/<USER>/repos/albert/moatless/evaluations\"\n", "evaluation_name = f\"{date_str}_moatless_{model_file_name}\"\n", "evaluation_dir = f\"{evaluations_dir}/{evaluation_name}\"\n", "trajectory_dir = f\"{evaluations_dir}/{evaluation_name}/trajs\"\n", "predictions_path = f\"{evaluation_dir}/all_preds.jsonl\"\n", "\n", "if not os.path.exists(trajectory_dir):\n", "    os.makedirs(trajectory_dir)\n", "\n", "litellm.success_callback = [\"langfuse\"]\n", "litellm.failure_callback = [\"langfuse\"]"], "outputs": [], "execution_count": 1}, {"cell_type": "code", "id": "a476221019e723c6", "metadata": {"ExecuteTime": {"end_time": "2024-06-17T18:02:31.378772Z", "start_time": "2024-06-17T18:02:27.448454Z"}}, "source": ["import logging\n", "import os\n", "import subprocess\n", "import time\n", "import traceback\n", "\n", "from moatless.benchmark.swebench import setup_swebench_repo, get_repo_dir_name, verify_search_trajectory\n", "\n", "from moatless.workspace import Workspace\n", "\n", "if not os.path.exists(trajectory_dir):\n", "    os.makedirs(trajectory_dir)\n", "\n", "def determine_status(info: dict) -> str:\n", "    if \"error\" in info:\n", "        return \"error\"\n", "    \n", "    if \"submission\" not in info or not info[\"submission\"]:\n", "        return \"not_generated\"\n", "    \n", "    prediction = info[\"submission\"]\n", "    \n", "    result_file = f\"{evaluation_dir}/result.json\"\n", "    if not os.path.exists(result_file) and prediction:\n", "        # No support for evaluation yet. Generate the swe bench evaluation result file and run again...\n", "        return \"generated\"\n", "    \n", "    with open(os.path.join(result_file), \"r\") as f:\n", "        report = json.load(f)\n", "        \n", "    if info[\"instance_id\"] in report[\"resolved\"]:\n", "        return \"resolved\"\n", "    else:\n", "        return \"failed\"\n", "\n", "def to_result(instance: dict, trajectory: dict, workspace: Workspace) -> dict:\n", "    info = trajectory[\"info\"]\n", "    \n", "    result = {\n", "        \"instance_id\": info[\"instance_id\"],\n", "        \"duration\": info[\"duration\"],\n", "        \"total_cost\": info[\"total_cost\"],\n", "        \"status\": determine_status(info),\n", "        \"transitions\": len(trajectory[\"transitions\"])\n", "    }\n", "    result.update(verify_search_trajectory(trajectory, instance, workspace))\n", "\n", "    return result\n"], "outputs": [], "execution_count": 2}, {"metadata": {"ExecuteTime": {"end_time": "2024-06-17T18:02:31.867686Z", "start_time": "2024-06-17T18:02:31.380940Z"}}, "cell_type": "code", "source": ["from moatless.edit.plan import PlanToCode\n", "from moatless.edit.edit import EditCode\n", "from moatless.loop import AgenticLoop\n", "from moatless.transitions import search_and_code_transitions\n", "from moatless.evaluation.utils import trace_metadata\n", "\n", "def evaluate(instance):\n", "    instance_id = instance[\"instance_id\"]\n", "\n", "    repo_dir = setup_swebench_repo(instance)\n", "    persist_dir = os.path.join(\n", "        index_store_dir, get_repo_dir_name(instance_id)\n", "    )\n", "    workspace = Workspace.from_dirs(repo_dir=repo_dir, index_dir=persist_dir)\n", "    \n", "    trajectory_path = os.path.join(trajectory_dir, f\"{instance_id}.json\")\n", "    if os.path.exists(trajectory_path):\n", "        with open(trajectory_path) as file:\n", "            trajectory = json.load(file)\n", "        if \"info\" in trajectory and trajectory[\"info\"].get(\"submission\") or \"error\" in trajectory[\"info\"]:\n", "            return to_result(instance, trajectory, workspace), trajectory\n", "\n", "    problem_statement = instance[\"problem_statement\"]\n", "    \n", "    metadata = trace_metadata(instance_id=instance_id, session_id=evaluation_name, trace_name=\"search_and_code\")\n", "    transitions = search_and_code_transitions(global_params={\"model\": model, \"max_iterations\": 15}, state_params={EditCode: {\"max_iterations\": 8}, PlanToCode: {\"max_iterations\": 8}})\n", "    \n", "    loop = AgenticLoop(transitions=transitions, workspace=workspace, metadata=metadata, trajectory_path=trajectory_path, max_cost=0.5, max_transitions=40, max_retries=6, max_message_tokens=14000, max_rejections=4)\n", "\n", "    info = {\n", "        \"evaluation_name\": evaluation_name,\n", "        \"instance_id\": instance[\"instance_id\"]\n", "    }\n", "    \n", "    start_time = time.time()\n", "    try:\n", "        response = loop.run(problem_statement)\n", "        \n", "    except Exception as e:\n", "        info[\"error\"] = traceback.format_exc()\n", "        logging.exception(f\"Error in evaluation of {instance['instance_id']} \")\n", "  \n", "    info[\"duration\"] = time.time() - start_time\n", "    info[\"total_cost\"] = loop.trajectory.total_cost()\n", "    \n", "    workspace.save()\n", "    \n", "    output = subprocess.run(\n", "          [\"git\", \"diff\"],\n", "          capture_output=True,\n", "          text=True,\n", "          cwd=repo_dir,\n", "    )\n", "    \n", "    info[\"submission\"] = output.stdout\n", "\n", "    loop.trajectory.save_info(info)\n", "    trajectory = loop.trajectory.to_dict()\n", "\n", "    return to_result(instance, trajectory, workspace), trajectory"], "id": "879884f581e28196", "outputs": [], "execution_count": 3}, {"cell_type": "code", "id": "ab446dc72a762649", "metadata": {"jupyter": {"is_executing": true}, "ExecuteTime": {"start_time": "2024-06-17T18:02:31.869784Z"}}, "source": ["from moatless.benchmark.swebench import sorted_instances\n", "import pandas as pd\n", "from tqdm.notebook import tqdm\n", "import json\n", "\n", "def run_evaluation(dataset_file: str, dataset: str = \"princeton-nlp/SWE-bench_Lite\", split=\"test\"):\n", "    if dataset_file:    \n", "        with open(dataset_file, \"r\") as f:\n", "            instances = json.load(f)\n", "\n", "        instances = sorted(instances, key=lambda x: len(x[\"resolved_by\"]), reverse=True)\n", "    else:\n", "        instances = sorted_instances(dataset, split)\n", "    \n", "    count = 0\n", "    identified = 0\n", "    generated = 0\n", "    error = 0\n", "    \n", "    sum_duration = 0\n", "    sum_total_cost = 0\n", "\n", "    with open(predictions_path, \"w\") as file:\n", "        file.write(\"\")\n", "\n", "    results = []\n", "    \n", "    stats = {}\n", "    pbar = tqdm(instances)\n", "    for instance in pbar:\n", "        result, trajectory = evaluate(instance)\n", "        if not result:\n", "            error += 1\n", "            continue\n", "    \n", "        sum_duration += result[\"duration\"]\n", "        sum_total_cost += result[\"total_cost\"]\n", "        \n", "        if result[\"status\"] == \"error\":\n", "            error += 1\n", "\n", "        if result[\"status\"] in [\"generated\", \"failed\", \"resolved\"]:\n", "            generated += 1\n", "            \n", "        if result[\"identified\"] is not None:\n", "            identified += 1\n", "        \n", "        count += 1\n", "\n", "        if sum_duration > 0:\n", "            stats[\"avg_duration\"] = sum_duration / count\n", "\n", "        if sum_total_cost > 0:\n", "            stats[\"avg_cost\"] = sum_total_cost / count\n", "            stats[\"total_cost\"] = sum_total_cost\n", "        \n", "        if identified > 0:\n", "            success_rate = (identified / count) * 100\n", "            stats[\"identified\"] = f\"{success_rate:.2f}%\"\n", "    \n", "        if generated > 0:\n", "            success_rate = (generated / count) * 100\n", "            stats[\"generated\"] = f\"{success_rate:.2f}%\"\n", "    \n", "        stats[\"error\"] = error\n", "        \n", "        pbar.set_postfix(stats)\n", "    \n", "        prediction = {\n", "            \"model_name_or_path\": evaluation_name,\n", "            \"instance_id\": instance[\"instance_id\"],\n", "            \"model_patch\": trajectory[\"info\"].get(\"submission\", \"\"),\n", "        }\n", "    \n", "        with open(predictions_path, \"a\") as file:\n", "            json_string = json.dumps(prediction)\n", "            file.write(json_string + \"\\n\")\n", "\n", "    return pd.DataFrame(results)\n", "df = run_evaluation(\"/home/<USER>/repos/albert/moatless/datasets/swebench_lite_all_evaluations.json\")\n", "\n", "# run_evaluation()"], "outputs": [{"data": {"text/plain": ["  0%|          | 0/300 [00:00<?, ?it/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "63fc527214f6476ea13a442fc0742b49"}}, "metadata": {}, "output_type": "display_data"}], "execution_count": null}, {"metadata": {"jupyter": {"is_executing": true}}, "cell_type": "code", "source": ["df.to_csv(f\"{evaluation_dir}/result.csv\", index=False, sep=';', decimal=',')\n", "df"], "id": "fc53faa8d41d161a", "outputs": [], "execution_count": null}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 5}