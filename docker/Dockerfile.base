FROM aorwall/sweb.base.py.x86_64:latest

COPY --from=ghcr.io/astral-sh/uv:latest /uv /uvx /bin/

# Ensure the installed binary is on the `PATH`
ENV PATH="/root/.local/bin/:$PATH"

RUN uv python install 3.12 --managed-python

RUN mkdir -p /data/nltk_data
ENV NLTK_DATA=/data/nltk_data

# Add a build arg that can be used to invalidate cache for git operations
RUN git clone https://github.com/aorwall/moatless-tools.git -b main /opt/moatless
RUN git clone https://github.com/aorwall/moatless-tree-search.git -b docker /opt/components

COPY docker/update-moatless.sh /usr/local/bin/update-moatless.sh
COPY docker/update-components.sh /usr/local/bin/update-components.sh

ENV MOATLESS_DIR=/data/moatless
ENV MOATLESS_COMPONENTS_PATH=/opt/components

WORKDIR /opt/moatless

RUN --mount=type=cache,target=/root/.cache/uv \
    uv sync --frozen --compile-bytecode --all-extras