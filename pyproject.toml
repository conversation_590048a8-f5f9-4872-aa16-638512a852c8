[build-system]
requires = ["pdm-backend"]
build-backend = "pdm.backend"

[project]
name = "moatless"
version = "0.1.0"
description = ""
readme = "README.md"

authors = [
    {name = "<PERSON>", email = "<EMAIL>"},
]
requires-python = "<3.13,>=3.10"
dependencies = [
    "pydantic<3.0.0,>=2.8.2",
    "tiktoken<1.0.0,>=0.8.0",
    "networkx<4.0,>=3.3",
    "tree-sitter==0.24.0",
    "tree-sitter-python==0.23.6",
    "tree-sitter-java==0.23.5",
    "rapidfuzz<4.0.0,>=3.9.5",
    "gitpython<4.0.0,>=3.1.43",
    "unidiff<1.0.0,>=0.7.5",
    "python-dotenv==1.0.1",
    "docstring-parser<1.0,>=0.16",
    "litellm<2.0.0,>=1.67.0",
    "openai<2.0.0,>=1.41.0",
    "anthropic<1.0.0,>=0.49.0",
    "llama-index<1.0.0,>=0.12.11",
    "llama-index-embeddings-openai<1.0.0,>=0.3.1",
    "llama-index-embeddings-voyageai<1.0.0,>=0.3.4",
    "llama-index-readers-file<1.0.0,>=0.4.3",
    "faiss-cpu<*******,>=1.8.0.post1",
    "voyageai<1.0.0,>=0.3.2",
    "filelock<4.0.0,>=3.16.1",
    "aiofiles<25.0.0,>=24.1.0",
    "swebench<4.0.0,>=3.0.15",
    "opentelemetry-sdk<2.0.0,>=1.30.0",
    "sqlalchemy>=2.0.41",
    "psycopg2-binary>=2.9.10",
    "redis<6.0.0,>=5.2.1",
    "gunicorn>=23.0.0",
    "opentelemetry-api<2.0.0,>=1.30.0",
    "opentelemetry-exporter-otlp<2.0.0,>=1.30.0",
    "opentelemetry-instrumentation<1.0,>=0.51b0",
    "opentelemetry-instrumentation-fastapi<1.0,>=0.51b0",
    "fastapi>=0.115.12",
    "uvicorn>=0.34.2",
    "dotenv>=0.9.9",
    "botocore<2.0.0,>=1.35.54",
    "boto3<2.0.0,>=1.35.54",
    "aioboto3<15.0.0,>=14.1.0",
    "kubernetes<33.0.0,>=32.0.0",
    "kubernetes-asyncio<33.0.0,>=32.0.0",
    "azure-storage-blob<13.0.0,>=12.25.0",
    "azure-monitor-opentelemetry<2.0.0,>=1.6.5",
]

[dependency-groups]
dev = [
    "pytest==8.3.2",
    "pytest-mock==3.14.0",
    "pytest-asyncio<1.0.0,>=0.25.3",
    "mypy==1.15.0",
    "ruff==0.5.5",
    "pylint<4.0.0,>=3.2.6",
    "ipykernel<7.0.0,>=6.29.5",
]

[tool.uv]
override-dependencies = [
    "llama-parse; sys_platform == 'never'",
]

[tool.ruff]
target-version = "py310"
extend-exclude = [ "tests", "evaluations", "notebooks",]
line-length = 120
