name: Run Tests

on:
  push:
    branches: [ '**' ] 
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    - name: Set up Python
      uses: actions/setup-python@v5
      with:
        python-version: '3.10'
    - name: Install uv
      uses: astral-sh/setup-uv@v5
      with:
        enable-cache: true
    - name: Install dependencies
      run: uv sync --all-extras
    
    - name: Cache REPO_DIR
      uses: actions/cache@v4
      with:
        path: /tmp/cache
        key: ${{ runner.os }}-moatless-${{ github.sha }}

    - name: Run tests
      env:
        OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}
        VOYAGE_API_KEY: ${{ secrets.VOYAGE_API_KEY }}
        INDEX_STORE_URL: ${{ vars.INDEX_STORE_URL }}
        INDEX_STORE_DIR: /tmp/cache/index-store
        REPO_DIR: /tmp/cache/repos
        MOATLESS_DIR: /tmp/moatless
      run: uv run pytest
    
    - name: Upload MOATLESS_DIR contents
      uses: actions/upload-artifact@v4
      if: ${{ success() && hashFiles('/tmp/moatless/**') != '' }}
      with:
        name: moatless-artifacts
        path: /tmp/moatless 